<template>
  <div class="w-full md:w-1/2 md:pl-3 mt-2">
    <PwSelectAutocomplete
      ref="inputStreetType"
      :lang="lang"
      :label="t('coverageForm.streetType.label')"
      :placeholder="t('coverageForm.streetType.placeholder')"
      name="street_type"
      :items="streetTypes"
      itemTitle="value"
      itemValue="key"
      v-model="modelValue"
      :value="modelValue?.value"
      :required="true"
      :disabled="disabled"
      :inputError="error"
      :classSelectorHeight="'max-h-36'"
      :customLabelClass="labelClasses"
      :customInputClass="inputClasses"
      @input="handleInput"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PwSelectAutocomplete, isEmptyValue } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { StreetType, Town } from '../types/address.types'

interface Props {
  modelValue: StreetType | null
  streetTypes: StreetType[]
  error: string
  lang: string
  theme: string
  town: Town | null
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: StreetType | null): void
  (e: 'input', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const { t } = useI18n()

// Computed para determinar si está deshabilitado
const disabled = computed(() => isEmptyValue(props.town))

// Clases dinámicas
const inputClasses = computed(() => {
  if (props.theme === 'danger') return '!border-danger'
  if (props.error) return '!border-danger'
  if (disabled.value) return '!placeholder-gray'
  return 'border-primary'
})

const labelClasses = computed(() => {
  if (props.theme === 'danger') return '!text-danger'
  if (props.error) return '!text-danger'
  if (disabled.value) return '!text-gray'
  return ''
})

const handleInput = (event: Event) => {
  emit('input', event)
}
</script>
