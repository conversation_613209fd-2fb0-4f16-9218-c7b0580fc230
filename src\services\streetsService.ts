import axios from 'axios'
import type { CancelTokenSource } from 'axios'

let source: CancelTokenSource = axios.CancelToken.source()
let pendingCall: boolean = false

const streetsService = {
  get(address: string, gescal7s: string[]) {
    pendingCall = true

    return axios
      .post(
        `${import.meta.env.VITE_BASE_URL}/coverage/api/addresses/streets`,
        {
          address,
          gescal7s
        },
        {
          cancelToken: source.token,
          headers: {
            'x-parlemapikey': import.meta.env.VITE_API_KEY_COVERAGE,
            'content-type': 'application/json-patch+json'
          }
        }
      )
      .then((response: any) => {
        pendingCall = false
        return response && response.data
      })
  },
  cancel(cancelMessage: string) {
    source.cancel(cancelMessage)
    pendingCall = false
    this.refreshToken()
    return this
  },
  refreshToken(): void {
    source = axios.CancelToken.source()
  },
  isPendingCall(): boolean {
    return pendingCall
  }
}

export default streetsService
