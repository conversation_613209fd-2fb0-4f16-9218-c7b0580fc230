<template>
  <div class="w-full md:w-1/2 md:pl-3 flex mt-2">
    <div class="relative w-full pr-3">
      <PwInputText
        ref="inputStreetNumber"
        :lang="lang"
        name="streetNumber"
        v-model="streetNumber"
        :value="streetNumber"
        type="number"
        :label="t('coverageForm.streetNumber.label')"
        :placeholder="t('coverageForm.streetNumber.placeholder')"
        :required="true"
        :disabled="numberDisabled"
        :customLabelClass="numberLabelClasses"
        :customInputClass="numberInputClasses"
        :inputError="streetNumberError"
        @input="handleStreetNumberInput"
      />
      <div class="absolute inset-y-0 right-0 flex items-start pr-3">
        <label for="snCheckbox" class="mr-2 mt-2 text-sm text-gray">S/N</label>
        <input
          type="checkbox"
          id="snCheckbox"
          v-model="snChecked"
          class="h-4 w-4 mt-2 border-gray"
          @change="handleSNChange"
          :disabled="checkboxDisabled"
        />
      </div>
    </div>

    <PwInputText
      ref="inputPostalCode"
      :lang="lang"
      name="zip"
      v-model="zip"
      :value="zip"
      type="number"
      :label="t('coverageForm.postalCode.label')"
      :placeholder="t('coverageForm.postalCode.placeholder')"
      :required="true"
      :disabled="zipDisabled"
      :inputError="zipError"
      :customLabelClass="zipLabelClasses"
      :customInputClass="zipInputClasses"
      class="w-full pl-3"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PwInputText, isEmptyValue } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { Street } from '../types/address.types'

interface Props {
  streetNumber: string
  zip: string
  snChecked: boolean
  street: Street | null
  streetNumberError: string
  zipError: string
  zipIsDisabled: boolean
  lang: string
  theme: string
}

interface Emits {
  (e: 'update:streetNumber', value: string): void
  (e: 'update:zip', value: string): void
  (e: 'update:snChecked', value: boolean): void
  (e: 'streetNumberInput', event: Event): void
  (e: 'snChange'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// Computed para estados de deshabilitado
const numberDisabled = computed(() => isEmptyValue(props.street) || props.snChecked)
const checkboxDisabled = computed(() => isEmptyValue(props.street))
const zipDisabled = computed(() => 
  (props.zipIsDisabled && !props.snChecked) || (!props.snChecked && isEmptyValue(props.streetNumber))
)

// Clases para el número de calle
const numberInputClasses = computed(() => {
  if (props.theme === 'danger') return '!border-danger'
  if (props.streetNumberError) return '!border-danger'
  if (numberDisabled.value) return '!placeholder-gray'
  return 'border-primary'
})

const numberLabelClasses = computed(() => {
  if (props.theme === 'danger') return '!text-danger'
  if (props.streetNumberError) return '!text-danger'
  if (numberDisabled.value) return '!text-gray'
  return ''
})

// Clases para el código postal
const zipInputClasses = computed(() => {
  if (props.theme === 'danger') return '!border-danger'
  if (props.zipError) return '!border-danger'
  if (zipDisabled.value) return '!placeholder-gray'
  return 'border-primary'
})

const zipLabelClasses = computed(() => {
  if (props.theme === 'danger') return '!text-danger'
  if (props.zipError) return '!text-danger'
  if (zipDisabled.value) return '!text-gray'
  return ''
})

// Handlers
const handleStreetNumberInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:streetNumber', target.value)
  emit('streetNumberInput', event)
}

const handleSNChange = () => {
  emit('snChange')
}
</script>
