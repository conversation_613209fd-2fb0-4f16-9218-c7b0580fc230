<template>
  <StreetMapFormWeb :config-data="configData"></StreetMapFormWeb>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'PwCoverageWeb'
})
</script>
<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import type { Ref } from 'vue'
import { useI18n } from 'vue-i18n'
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import StreetMapFormWeb from '../components/street-map-form-web/StreetMapFormWeb.vue'
import { changeColors } from 'parlem-webcomponents-common'
import type { IConfigData } from '@/interfaces/config-data.interface'

const props = defineProps({
  config: String
})
const configData: IConfigData = JSON.parse(props.config || '{"lang":"ca","company":"Parlem"}')
const lang: Ref<string | undefined> = ref(configData.lang as string)
const { locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang.value || locale.value
const isLoading: Ref<boolean> = ref(false)

onBeforeMount(() => {
  isLoading.value = true
  if (configData.company) {
    changeColors(configData.company ? configData.company : 'Parlem')
  }
  isLoading.value = false
})
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
