import { createApp } from 'vue'
import App from '@/App.vue'

import { defineCustomElementWrapped } from '@/customElementWrapper'
import { PwCoverage, PwCoverageOldCheck, PwStreetMap, PwCoverageWeb } from '@/webcomponents'
import store from '@/store'
import i18n from '@/assets/i18n'
import '@/assets/styles/index.css'

const PwCoverageCE = defineCustomElementWrapped(PwCoverage, [store, i18n])
const PwCoverageOldCheckCE = defineCustomElementWrapped(PwCoverageOldCheck, [store, i18n])
const PwStreetMapCE = defineCustomElementWrapped(PwStreetMap, [store, i18n])
const PwCoverageWebCE = defineCustomElementWrapped(PwCoverageWeb, [store, i18n])

export { PwCoverageCE, PwCoverageOldCheckCE, PwStreetMapCE, PwCoverageWebCE }

declare module 'vue' {
  export interface GlobalComponents {
    PwCoverageCE: typeof PwCoverageCE
    PwCoverageOldCheckCE: typeof PwCoverageOldCheckCE
    PwStreetMapCE: typeof PwStreetMapCE
    PwCoverageWebCE: typeof PwCoverageWebCE
  }
}

customElements.define('pw-coverage', PwCoverageCE)
customElements.define('pw-coverage-old', PwCoverageOldCheckCE)
customElements.define('pw-street-map', PwStreetMapCE)
customElements.define('pw-coverage-web', PwCoverageWebCE)

createApp(App).use(store).use(i18n).mount('#app-coverage')
