import { ref } from 'vue'
import {
  buildingsService,
  locationsService,
  streetsService,
  townsService,
  unitsService
} from '@/services'
import type { Town, Street, AddressUnit } from '../types/address.types'
import { useI18n } from 'vue-i18n'
import listFilteredObjectsValue from '@/utils/list-filtered-objects-value'
import { mapTownData, mapStreetData, VALIDATION_CONSTANTS } from '../utils/addressUtils'

export function useAddressServices() {
  const { t } = useI18n()

  // Estados de carga
  const loadingTowns = ref(false)
  const loadingStreets = ref(false)
  const loadingUnits = ref(false)

  // Servicio para obtener pueblos/ciudades
  const fetchTowns = async (address: string): Promise<{ data: Town[], error: string | null }> => {
    if (address.length < VALIDATION_CONSTANTS.MIN_TOWN_SEARCH_LENGTH) {
      return { data: [], error: null }
    }

    loadingTowns.value = true
    
    try {
      const townResult = await townsService
        .cancel('Re-executed call')
        .get(address)

      if (townResult) {
        const towns = townResult.map(mapTownData)
        loadingTowns.value = false
        return { data: towns, error: null }
      }
      
      loadingTowns.value = false
      return { data: [], error: null }
    } catch (error: any) {
      loadingTowns.value = false
      if (error.name !== 'CanceledError') {
        console.error('Error fetching towns:', error)
        return { data: [], error: t('coverageForm.inputError') }
      }
      return { data: [], error: null }
    }
  }

  // Servicio para obtener gescal7s
  const fetchGescal7s = async (address: string, ineCode: string): Promise<{ data: string[], error: string | null }> => {
    if (!address.length || !ineCode.length) {
      return { data: [], error: null }
    }

    try {
      const locationResponse = await locationsService
        .cancel('Re-executed call')
        .get(address, ineCode)

      const gescal7s = locationResponse?.map((location: any) => location.gescal7) || []
      return { data: gescal7s, error: null }
    } catch (error: any) {
      if (error.name !== 'CanceledError') {
        console.error('Error fetching gescal7s:', error)
        return { data: [], error: t('coverageForm.inputError') }
      }
      return { data: [], error: null }
    }
  }

  // Servicio para obtener calles
  const fetchStreets = async (address: string, gescal7s: string[]): Promise<{ data: Street[], error: string | null }> => {
    if (!address.length || !gescal7s.length) {
      return { data: [], error: null }
    }

    loadingStreets.value = true

    try {
      const streetsResponse = await streetsService
        .cancel('Re-executed call')
        .get(address, gescal7s)

      if (streetsResponse) {
        const streets = streetsResponse.map(mapStreetData)
        loadingStreets.value = false
        return { data: streets, error: null }
      }
      
      loadingStreets.value = false
      return { data: [], error: null }
    } catch (error: any) {
      loadingStreets.value = false
      if (error.name !== 'CanceledError') {
        console.error('Error fetching streets:', error)
        return { data: [], error: t('coverageForm.inputError') }
      }
      return { data: [], error: null }
    }
  }

  // Servicio para obtener unidades de dirección
  const fetchUnits = async (gescal17: string): Promise<{ 
    data: {
      units: any[],
      building: any,
      bis: AddressUnit[],
      blocks: AddressUnit[],
      gates: AddressUnit[],
      letters: AddressUnit[],
      stairs: AddressUnit[],
      floors: AddressUnit[],
      firstHands: AddressUnit[],
      secondHands: AddressUnit[]
    }, 
    error: string | null 
  }> => {
    if (!gescal17?.length) {
      return { 
        data: createEmptyUnitsData(), 
        error: null 
      }
    }

    loadingUnits.value = true

    try {
      const unitsResponse = await unitsService
        .cancel('Re-executed call')
        .get(gescal17)

      if (unitsResponse && unitsResponse.items) {
        const units = unitsResponse.items
        const building = unitsResponse.building

        // Extraer las diferentes opciones usando la función utilitaria
        const addressUnits = extractAddressUnits(units)

        loadingUnits.value = false
        return { 
          data: { 
            units, 
            building, 
            ...addressUnits
          }, 
          error: null 
        }
      }

      loadingUnits.value = false
      return { 
        data: createEmptyUnitsData(), 
        error: null 
      }
    } catch (error: any) {
      loadingUnits.value = false
      if (error.name !== 'CanceledError') {
        console.error('Error fetching units:', error)
        return { 
          data: createEmptyUnitsData(), 
          error: t('coverageForm.inputError') 
        }
      }
      return { 
        data: createEmptyUnitsData(), 
        error: null 
      }
    }
  }

  // Función auxiliar para crear datos vacíos de unidades
  const createEmptyUnitsData = () => ({
    units: [], 
    building: {}, 
    bis: [], 
    blocks: [], 
    gates: [], 
    letters: [], 
    stairs: [], 
    floors: [], 
    firstHands: [], 
    secondHands: [] 
  })

  // Función auxiliar para extraer unidades de dirección
  const extractAddressUnits = (units: any[]) => {
    const unitTypes = ['bis', 'block', 'gate', 'letter', 'stair', 'floor', 'firstHand', 'secondHand']
    const result: any = {}

    unitTypes.forEach(type => {
      result[`${type}s`] = listFilteredObjectsValue({
        items: units,
        compareData: [{ propertyFilter: type }],
        propertyData: type
      })
    })

    return result
  }

  return {
    // Estados de carga
    loadingTowns,
    loadingStreets,
    loadingUnits,
    
    // Servicios
    fetchTowns,
    fetchGescal7s,
    fetchStreets,
    fetchUnits,
    
    // Utilidades
    numberRegex: VALIDATION_CONSTANTS.NUMBER_REGEX
  }
}
