<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <script src="./pwc-coverage.umd.min.js"></script>
    <title>
        PWC-COVERAGE
    </title>
</head>

<pwc-coverage-hello></pwc-coverage-hello>
<div class="webcomponent-card">
    <h1>pwc-coverage-streetmap</h1>
    <pwc-coverage-streetmap config='{"theme":"Parlem", "spainOption": true}'></pwc-coverage-streetmap>
</div>
<div class="webcomponent-card">
    <h1>pwc-coverage-lookup</h1>
    <pwc-coverage-lookup config='{"theme":"Parlem", "extended": true}'></pwc-coverage-lookup>
</div>
<div class="webcomponent-card">
    <h1>pwc-coverage-add-coverage</h1>
    <pwc-coverage-add-coverage config='{"theme":"Parlem"}'></pwc-coverage-add-coverage>
</div>
<script>
    window.addEventListener('coverage-found', (event) => {
        console.info(event.detail)
    })
    window.addEventListener('coverage-not-found', (event) => {
        console.info(event.detail)
    })
</script>
<style>
    .webcomponent-card {
        border: 1px solid #ffcc00;
        width: 750px;
        min-width: 300px;
        max-width: 80%;
        margin: 30px auto;
        position: relative;
        padding: 40px;
        border-radius: 10px;
    }

    .webcomponent-card h1 {
        padding: 0px 10px;
        position: absolute;
        top: -10px;
        background-color: white;
    }
</style>