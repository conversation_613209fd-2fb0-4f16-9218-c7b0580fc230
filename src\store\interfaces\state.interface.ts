import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { ILocation } from '@/interfaces/location.interface'
import type { IStreet } from '@/interfaces/street.interface'
import type { ITown } from '@/interfaces/town.interface'

export interface IState {
  coverage: ICoverage | null
  validCoverage: boolean
  coverageResult: any | null
  town: ITown | null
  locations: ILocation[] | null
  street: IStreet | null
  building: any | null
  showErrorMessage: boolean
  gescal17: string | null
}
