
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width,initial-scale=1.0">
		<link rel="icon" href=https://pwc.parlem.com/coverage/%3C%=%20BASE_URL%20%%3Efavicon.ico">
		<script src="https://pwc.parlem.com/coverage/pwc-coverage.umd.min.js"></script>
		<title>
			APROOP CONSULTA COBERTURA
		</title>
	</head>

    <pwc-coverage-hello></pwc-coverage-hello>
    <div class="webcomponent-card">
        <h1>Guia de carrers</h1>
        <pwc-coverage-streetmap config='{"theme":"Aproop", "spainOption": true}'></pwc-coverage-streetmap>
    </div>
    <div class="webcomponent-card" id="lookup">
        <h1>LLista cobertures disponibles</h1>
        <pwc-coverage-lookup config='{"theme":"Aproop", "extended": true}'></pwc-coverage-lookup>
    </div>
    <!-- <div class="webcomponent-card">
        <h1>pwc-coverage-add-coverage</h1>
        <pwc-coverage-add-coverage config='{"theme":"Aproop"}'></pwc-coverage-add-coverage>
    </div> -->
    <script>
        
        window.addEventListener('coverage-found', (event) => {
            console.info(event.detail)
        })
        window.addEventListener('coverage-not-found', (event) => {
            console.info(event.detail)
        })

        const aproopColor = '#e2ff00'

        //Pwc Coverage Hello
        const pwcCoverageHello = document.getElementsByTagName("pwc-coverage-hello")[0].shadowRoot
        const title = pwcCoverageHello.querySelector(".hello h1 b")
        const version = pwcCoverageHello.querySelector(".version")
        
        title.style.color = aproopColor
        version.style.backgroundColor = aproopColor
        
        const pwcCoverageStreetmap = document.getElementsByTagName("pwc-coverage-streetmap")[0].shadowRoot
        const buscador = pwcCoverageStreetmap.querySelector(".pwc-flex.--column.--center")
        buscador.style.display = "none"


        //Pwc Coverage Lookup
        const pwcCoverageLookup = document.getElementsByTagName("pwc-coverage-lookup")[0].shadowRoot
        const coverageFilter = ['Direct', 'Neba', 'Vula', 'Indirect']
        let coverageTypes = []

        function includesCoverageText(text, textToFind){
            const coverageDirectOrIndirect = text.includes(textToFind)
            if (coverageDirectOrIndirect){
                text.includes('Direct')? coverageTypes.push('Directe') : coverageTypes.push('Indirecte')
            }
            return coverageDirectOrIndirect
        }

       function checkCoverageType (text) {
           return coverageFilter.some (filter => includesCoverageText(text, filter))
        }

        function filterCoverageType(){
           const coverageElements = pwcCoverageLookup.querySelectorAll(".pwc-flex > div")
            coverageElements.forEach((node)=>{
                if(node.firstChild.className && node.firstChild.className==="coverage-list-title" && !checkCoverageType(node.outerText)){
                    node.style.display = 'none'
                } else if (node.className==='coverage-list' && node.childNodes.length>1){
                    node.childNodes.forEach(child=>{
                        child.innerHTML && !checkCoverageType(child.innerHTML)? child.style.display = 'none':''
                    })
                }
            })
        }

        function changeCoverageColor (){
            const lookupChildNodes = Array.from(pwcCoverageLookup.childNodes)
            const coverageItems =  pwcCoverageLookup.querySelectorAll(".coverage-list-title")
            coverageItems.forEach(element => element.style.borderBottom = `1px solid ${aproopColor}`)
        }

        function hideContinueButton (){
            const lookupChildNodes = Array.from(pwcCoverageLookup.childNodes)
            const button =  pwcCoverageLookup.querySelector(".primary")
            button ? button.style.display = 'none':''
        }

        // Pwc Streetmap functions
        function detectCoverageStreetmap (){
            const coverage = pwcCoverageStreetmap.querySelectorAll(".ui-list__item__coverage")
            coverage.forEach((node)=>{
                if(node.innerHTML && (!checkCoverageType(node.innerHTML)||node.innerHTML.includes('Adamo'))){
                    node.innerHTML = `<span>${coverageTypes[0]}</span>`
                }
            })
        }

        // Quan es selecciona un pis i canvia el tamany del div de la llista de cobertures
        let resizeObserverLookup = new ResizeObserver(() => {
            coverageTypes = []
            filterCoverageType()
            detectCoverageStreetmap()
            changeCoverageColor()
            hideContinueButton ()
        });
        const lookupContainer = document.querySelector("#lookup")
        resizeObserverLookup.observe(lookupContainer);


    </script>
<style>
    .webcomponent-card {
        border: 1px solid #e2ff00;
        width: 750px;
        min-width: 300px;
        max-width: 80%;
        margin: 30px auto;
        position: relative;
        padding: 60px 40px 40px;
        border-radius: 10px;
    }
    .webcomponent-card h1 {
        padding: 0px 10px;
        position: absolute;
        top: -10px;
        background-color: white;
    }
</style></html>