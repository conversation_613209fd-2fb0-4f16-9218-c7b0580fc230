<template>
  <form>
    <main v-for="(field, index) in formFields" :key="index">
      <PwSelectAutocomplete
        v-if="field.type === 'selector'"
        v-model="field.value"
        :id="field.key"
        :value="field?.value"
        :item-title="field.itemTitle"
        :item-value="field.itemValue"
        :label="`${field.label}${field.validations?.includes('required') ? '*' : ''}`"
        :placeholder="field.placeholder"
        :items="field.options"
        :classSelectorHeight="'max-h-36'"
        :customInputClass="
          theme === 'danger'
            ? '!border-danger'
            : checkIsBeforeFieldEmptyValue(index)
            ? '!placeholder-gray'
            : 'border-primary'
        "
        :custom-label-class="
          theme === 'danger'
            ? '!text-danger'
            : checkIsBeforeFieldEmptyValue(index)
            ? '!text-gray'
            : ''
        "
        :validations="field.validations"
        :inputError="t(`${field.error}`)"
        :modelObject="true"
        :disabled="checkIsBeforeFieldEmptyValue(index)"
        @input="(event:any) => watchSelector(event, field, index)"
      ></PwSelectAutocomplete>
      <p>{{ field.value }}{{ index }}</p>
    </main>
    <PwButton type="submit"></PwButton>
  </form>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'StreetMapFormWeb'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { Ref } from 'vue'
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import {
  PwInputText,
  PwSelectAutocomplete,
  filterArrayByStringValue,
  padStart,
  isEmptyValue
} from 'parlem-webcomponents-common'
import type { IConfigData } from '@/interfaces/config-data.interface'

const props = defineProps({
  config: String,
  data: String
})

const store: Store<any> = useStore()
const configData: IConfigData = JSON.parse(props.config || '{"lang":"ca","company":"Parlem"}')
const lang: string | undefined = configData.lang as string
const theme: string | undefined = configData.theme as string

const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value

const formFields = ref([
  {
    name: 'town',
    key: 'towns',
    itemTitle: 'fullTownName',
    itemValue: 'ineCode',
    type: 'selector',
    value: undefined,
    label: t('coverageForm.town.label'),
    placeholder: t('coverageForm.town.placeholder'),
    width: '1/2',
    options: [],
    validations: ['required'],
    error: ''
  },
  {
    name: 'street',
    key: 'streets',
    itemTitle: 'fullStreetName',
    itemValue: 'gescal12',
    type: 'selector',
    value: undefined,
    label: t('coverageForm.street.label'),
    placeholder: t('coverageForm.street.placeholder'),
    width: '1/2',
    options: [],
    validations: ['required'],
    error: ''
  }
])

const watchSelector = (event: any, field: any, index: number) => {
  if (field.value) {
    field.value = undefined
  }
  const inputValue = event.target.value

  if (inputValue.length >= 2) {
    const payloadAction = { value: inputValue, field, index }
    const selectedField = formFields.value[index - 1]
    if (selectedField) {
      payloadAction.index = index
    }
    setSelectorOptions(payloadAction)
  }
}

const setSelectorOptions = async (payloadAction: any) => {
  await store.dispatch(
    `set${payloadAction.field.key.charAt(0).toUpperCase() + payloadAction.field.key.slice(1)}`,
    payloadAction
  )
}

const checkIsBeforeFieldEmptyValue = (index: number): boolean => {
  const selectedField = formFields.value[index - 1]
  return selectedField ? isEmptyValue(selectedField.value) : false
}
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
