<template>
  <div v-if="!snChecked && showAdditionalInputs" class="grid gap-4 grid-cols-2 md:grid-cols-8 mt-2">
    <!-- Bis -->
    <PwSelectAutocomplete
      v-if="currentBis.length > 0"
      ref="inputBis"
      :lang="lang"
      :label="t('coverageForm.bis.label')"
      :placeholder="t('coverageForm.bis.placeholder')"
      name="bis"
      :items="currentBis"
      v-model="bis"
      :value="bis?.bis"
      item-title="bis"
      item-value="bis"
      :required="true"
      :disabled="bisDisabled"
      :customLabelClass="bisLabelClasses"
      :customInputClass="bisInputClasses"
      :inputError="bisError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('bisInput', $event)"
    />

    <!-- Block -->
    <PwSelectAutocomplete
      v-if="currentBlocks.length > 0"
      ref="inputBlock"
      :lang="lang"
      :label="t('coverageForm.block.label')"
      :placeholder="t('coverageForm.block.placeholder')"
      name="block"
      :items="currentBlocks"
      v-model="block"
      :value="block?.block"
      item-title="block"
      item-value="block"
      :required="true"
      :disabled="blockDisabled"
      :customLabelClass="blockLabelClasses"
      :customInputClass="blockInputClasses"
      :inputError="blockError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('blockInput', $event)"
    />

    <!-- Gate -->
    <PwSelectAutocomplete
      v-if="currentGates.length > 0"
      ref="inputGate"
      :lang="lang"
      :label="t('coverageForm.gate.label')"
      :placeholder="t('coverageForm.gate.placeholder')"
      name="gate"
      :items="currentGates"
      v-model="gate"
      :value="gate?.gate"
      item-title="gate"
      item-value="gate"
      :required="true"
      :disabled="gateDisabled"
      :customLabelClass="gateLabelClasses"
      :customInputClass="gateInputClasses"
      :inputError="gateError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('gateInput', $event)"
    />

    <!-- Letter -->
    <PwSelectAutocomplete
      v-if="currentLetters.length > 0"
      ref="inputLetter"
      :lang="lang"
      :label="t('coverageForm.letter.label')"
      :placeholder="t('coverageForm.letter.placeholder')"
      name="letter"
      :items="currentLetters"
      v-model="letter"
      :value="letter?.letter"
      item-title="letter"
      item-value="letter"
      :required="true"
      :disabled="letterDisabled"
      :customLabelClass="letterLabelClasses"
      :customInputClass="letterInputClasses"
      :inputError="letterError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('letterInput', $event)"
    />

    <!-- Stair -->
    <PwSelectAutocomplete
      v-if="currentStairs.length > 0"
      ref="inputStair"
      :lang="lang"
      :label="t('coverageForm.stair.label')"
      :placeholder="t('coverageForm.stair.placeholder')"
      name="stair"
      :items="currentStairs"
      v-model="stair"
      :value="stair?.stair"
      item-title="stair"
      item-value="stair"
      :required="true"
      :disabled="stairDisabled"
      :customLabelClass="stairLabelClasses"
      :customInputClass="stairInputClasses"
      :inputError="stairError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('stairInput', $event)"
    />

    <!-- Floor -->
    <PwSelectAutocomplete
      v-if="currentFloors.length > 0"
      ref="inputFloor"
      :lang="lang"
      :label="t('coverageForm.floor.label')"
      :placeholder="t('coverageForm.floor.placeholder')"
      name="floor"
      :items="currentFloors"
      v-model="floor"
      :value="floor?.floor"
      item-title="floor"
      item-value="floor"
      :required="true"
      :disabled="floorDisabled"
      :customLabelClass="floorLabelClasses"
      :customInputClass="floorInputClasses"
      :inputError="floorError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('floorInput', $event)"
    />

    <!-- First Hand -->
    <PwSelectAutocomplete
      v-if="currentFirstHands.length > 0"
      ref="inputFirstHand"
      :lang="lang"
      :label="t('coverageForm.firstHand.label')"
      :placeholder="t('coverageForm.firstHand.placeholder')"
      name="firstHand"
      :items="currentFirstHands"
      v-model="firstHand"
      :value="firstHand?.firstHand"
      item-title="firstHand"
      item-value="firstHand"
      :required="true"
      :disabled="firstHandDisabled"
      :customLabelClass="firstHandLabelClasses"
      :customInputClass="firstHandInputClasses"
      :inputError="firstHandError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('firstHandInput', $event)"
    />

    <!-- Second Hand -->
    <PwSelectAutocomplete
      v-if="currentSecondHands.length > 0"
      ref="inputSecondHand"
      :lang="lang"
      :label="t('coverageForm.secondHand.label')"
      customLabelClass="opacity-0"
      :placeholder="t('coverageForm.secondHand.placeholder')"
      name="secondHand"
      :items="currentSecondHands"
      v-model="secondHand"
      :value="secondHand?.secondHand"
      item-title="secondHand"
      item-value="secondHand"
      :required="true"
      :disabled="secondHandDisabled"
      :customInputClass="secondHandInputClasses"
      :inputError="secondHandError"
      :classSelectorHeight="'max-h-28'"
      @input="$emit('secondHandInput', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PwSelectAutocomplete, isEmptyValue } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { AddressUnit } from '../types/address.types'

interface Props {
  // Form data
  bis: AddressUnit | null
  block: AddressUnit | null
  gate: AddressUnit | null
  letter: AddressUnit | null
  stair: AddressUnit | null
  floor: AddressUnit | null
  firstHand: AddressUnit | null
  secondHand: AddressUnit | null
  
  // Options
  currentBis: AddressUnit[]
  currentBlocks: AddressUnit[]
  currentGates: AddressUnit[]
  currentLetters: AddressUnit[]
  currentStairs: AddressUnit[]
  currentFloors: AddressUnit[]
  currentFirstHands: AddressUnit[]
  currentSecondHands: AddressUnit[]
  
  // Errors
  bisError: string
  blockError: string
  gateError: string
  letterError: string
  stairError: string
  floorError: string
  firstHandError: string
  secondHandError: string
  
  // State
  zip: string
  snChecked: boolean
  showAdditionalInputs: boolean
  lang: string
  theme: string
}

interface Emits {
  (e: 'update:bis', value: AddressUnit | null): void
  (e: 'update:block', value: AddressUnit | null): void
  (e: 'update:gate', value: AddressUnit | null): void
  (e: 'update:letter', value: AddressUnit | null): void
  (e: 'update:stair', value: AddressUnit | null): void
  (e: 'update:floor', value: AddressUnit | null): void
  (e: 'update:firstHand', value: AddressUnit | null): void
  (e: 'update:secondHand', value: AddressUnit | null): void
  (e: 'bisInput', event: Event): void
  (e: 'blockInput', event: Event): void
  (e: 'gateInput', event: Event): void
  (e: 'letterInput', event: Event): void
  (e: 'stairInput', event: Event): void
  (e: 'floorInput', event: Event): void
  (e: 'firstHandInput', event: Event): void
  (e: 'secondHandInput', event: Event): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()
const numberRegex = /^[0-9]*$/

// Computed para validación de ZIP
const isZipInvalid = computed(() => {
  return isEmptyValue(props.zip) ||
         (props.zip && props.zip.length < 5) ||
         (props.zip && !numberRegex.test(props.zip))
})

// Computed para estados de deshabilitado
const bisDisabled = computed(() => {
  return isZipInvalid.value || props.currentBis.length === 0
})

const blockDisabled = computed(() => {
  return isZipInvalid.value ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         props.currentBlocks.length === 0
})

const gateDisabled = computed(() => {
  return isZipInvalid.value ||
         isEmptyValue(props.currentGates) ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         (!isEmptyValue(props.currentBlocks) && props.block === null)
})

const letterDisabled = computed(() => {
  return isZipInvalid.value ||
         isEmptyValue(props.currentLetters) ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         (!isEmptyValue(props.currentBlocks) && props.block === null) ||
         (!isEmptyValue(props.currentGates) && props.gate === null)
})

const stairDisabled = computed(() => {
  return isZipInvalid.value ||
         isEmptyValue(props.currentStairs) ||
         (!isEmptyValue(props.currentLetters) && props.letter === null) ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         (!isEmptyValue(props.currentBlocks) && props.block === null) ||
         (!isEmptyValue(props.currentGates) && props.gate === null)
})

const floorDisabled = computed(() => {
  return isZipInvalid.value ||
         (isEmptyValue(props.floor) && isEmptyValue(props.currentFloors)) ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         (!isEmptyValue(props.currentBlocks) && props.block === null) ||
         (!isEmptyValue(props.currentGates) && props.gate === null) ||
         (!isEmptyValue(props.currentLetters) && props.letter === null) ||
         (!isEmptyValue(props.currentStairs) && props.stair === null)
})

const firstHandDisabled = computed(() => {
  return isZipInvalid.value ||
         (isEmptyValue(props.firstHand) && isEmptyValue(props.currentFirstHands)) ||
         (!isEmptyValue(props.currentBlocks) && props.block === null) ||
         (!isEmptyValue(props.currentLetters) && props.letter === null) ||
         (!isEmptyValue(props.currentStairs) && props.stair === null) ||
         (!isEmptyValue(props.currentFloors) && props.floor === null) ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         (!isEmptyValue(props.currentGates) && props.gate === null)
})

const secondHandDisabled = computed(() => {
  return isZipInvalid.value ||
         (isEmptyValue(props.secondHand) && isEmptyValue(props.currentSecondHands)) ||
         (!isEmptyValue(props.currentBis) && props.bis === null) ||
         (!isEmptyValue(props.currentBlocks) && props.block === null) ||
         (!isEmptyValue(props.currentGates) && props.gate === null) ||
         (!isEmptyValue(props.currentLetters) && props.letter === null) ||
         (!isEmptyValue(props.currentStairs) && props.stair === null) ||
         (!isEmptyValue(props.currentFloors) && props.floor === null) ||
         (!isEmptyValue(props.currentFirstHands) && props.firstHand === null)
})

// Funciones para generar clases dinámicas
const getInputClasses = (hasError: boolean, isDisabled: boolean) => {
  if (props.theme === 'danger') return '!border-danger'
  if (hasError) return '!border-danger'
  if (isDisabled) return '!placeholder-gray'
  return 'border-primary'
}

const getLabelClasses = (hasError: boolean, isDisabled: boolean) => {
  if (props.theme === 'danger') return '!text-danger'
  if (hasError) return '!text-danger'
  if (isDisabled) return '!text-gray'
  return ''
}

// Clases para cada campo
const bisInputClasses = computed(() => getInputClasses(!!props.bisError, bisDisabled.value))
const bisLabelClasses = computed(() => getLabelClasses(!!props.bisError, bisDisabled.value))

const blockInputClasses = computed(() => getInputClasses(!!props.blockError, blockDisabled.value))
const blockLabelClasses = computed(() => getLabelClasses(!!props.blockError, blockDisabled.value))

const gateInputClasses = computed(() => getInputClasses(!!props.gateError, gateDisabled.value))
const gateLabelClasses = computed(() => getLabelClasses(!!props.gateError, gateDisabled.value))

const letterInputClasses = computed(() => getInputClasses(!!props.letterError, letterDisabled.value))
const letterLabelClasses = computed(() => getLabelClasses(!!props.letterError, letterDisabled.value))

const stairInputClasses = computed(() => getInputClasses(!!props.stairError, stairDisabled.value))
const stairLabelClasses = computed(() => getLabelClasses(!!props.stairError, stairDisabled.value))

const floorInputClasses = computed(() => getInputClasses(!!props.floorError, floorDisabled.value))
const floorLabelClasses = computed(() => getLabelClasses(!!props.floorError, floorDisabled.value))

const firstHandInputClasses = computed(() => getInputClasses(!!props.firstHandError, firstHandDisabled.value))
const firstHandLabelClasses = computed(() => getLabelClasses(!!props.firstHandError, firstHandDisabled.value))

const secondHandInputClasses = computed(() => getInputClasses(!!props.secondHandError, secondHandDisabled.value))
</script>
