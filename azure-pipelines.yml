# Node.js with Vue
# Build a Node.js project that uses Vue.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
variables:
  azureSubscription: AzureSubscription
  version: v$(Build.BuildId)
  isMain: $[eq(variables['Build.SourceBranch'], 'refs/heads/master')]    
trigger:
- develop
- master
pool:
  vmImage: ubuntu-latest
name: v$(Build.BuildId)
stages:
  - stage: Artifacts
    displayName: 'Build'
    jobs:
    - job: "BuildArtifact"
      steps:
      # npm
      - task: NodeTool@0
        inputs:
          versionSpec: '18.18.2'
        displayName: 'Build'
      - script: |
          npm install -g pnpm
          npx update-browserslist-db@latest
          npm install
          npm run build
        displayName: 'npm install and build'
      - task: Npm@1
        inputs:
          command: 'publish'
          verbose: true
          publishRegistry: 'useFeed'
          publishFeed: '1ceb3ac7-678b-4c27-a276-7646b0b53fb9/d30e8b3c-b326-429e-9975-6a7a55b146c5'
        condition: and(succeeded(), eq(variables.isMain, true))           
      - task: CopyFiles@1
        displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
        inputs:
          SourceFolder: ./dist
          TargetFolder: '$(Build.ArtifactStagingDirectory)/coverage/dist'
        condition: and(succeeded(), eq(variables.isMain, true))           
      - task: PublishPipelineArtifact@1
        inputs:
          targetPath: $(Build.ArtifactStagingDirectory)/coverage/dist
          artifactName: pwcoverage
        condition: and(succeeded(), eq(variables.isMain, true))           

