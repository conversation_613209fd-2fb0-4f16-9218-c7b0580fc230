<template>
  <div class="flex flex-wrap">
    <div class="w-full md:w-1/2 md:pr-3 mt-2">
      <PwSelectAutocomplete
        ref="inputTown"
        :lang="lang"
        :label="t('coverageForm.town.label')"
        :placeholder="t('coverageForm.town.placeholder')"
        name="town"
        id="town"
        :items="towns"
        itemTitle="fullTownName"
        itemValue="ineCode"
        v-model="town"
        :value="town?.fullTownName"
        :modelObject="true"
        :required="true"
        :inputError="inputTownError"
        :classSelectorHeight="'max-h-36'"
        :customInputClass="theme === 'danger' ? '!border-danger' : 'border-primary'"
        :custom-label-class="theme === 'danger' ? '!text-danger' : 'text-primary'"
        @input="watchInputTownValue"
      />
    </div>
    <div class="w-full md:w-1/2 md:pl-3 mt-2">
      <PwSelectAutocomplete
        ref="inputStreetType"
        :lang="lang"
        :label="t('coverageForm.streetType.label')"
        :placeholder="t('coverageForm.streetType.placeholder')"
        name="street_type"
        id="street_type"
        :items="streetTypes"
        itemTitle="value"
        itemValue="key"
        v-model="streetType"
        :value="streetType?.value"
        :required="true"
        :disabled="isEmptyValue(town)"
        :inputError="inputStreetTypeError"
        :classSelectorHeight="'max-h-36'"
        :customLabelClass="
          theme === 'danger' ? '!text-danger' : isEmptyValue(town) ? '!text-gray' : ''
        "
        :customInputClass="
          theme === 'danger'
            ? '!border-danger'
            : isEmptyValue(town)
            ? '!placeholder-gray'
            : 'border-primary'
        "
        @input="watchInputStreetTypeValue"
      />
    </div>
  </div>
  <div class="flex flex-wrap">
    <div class="w-full md:w-1/2 md:pr-3 mt-2">
      <PwSelectAutocomplete
        ref="inputStreet"
        :lang="lang"
        :label="t('coverageForm.street.label')"
        :placeholder="t('coverageForm.street.placeholder')"
        name="street"
        id="street"
        :items="streets"
        itemTitle="fullStreetName"
        itemValue="gescal12"
        v-model="street"
        :value="street?.fullStreetName"
        :required="true"
        :disabled="isEmptyValue(town)"
        :inputError="inputStreetError"
        :classSelectorHeight="'max-h-36'"
        :customLabelClass="
          theme === 'danger' ? '!text-danger' : isEmptyValue(town) ? '!text-gray' : ''
        "
        :customInputClass="
          theme === 'danger'
            ? '!border-danger'
            : isEmptyValue(town)
            ? '!placeholder-gray'
            : 'border-primary'
        "
        @input="watchInputStreetValue"
      />
    </div>
    <div class="w-full md:w-1/2 md:pl-3 flex mt-2">
      <div class="relative w-full pr-3">
        <PwInputText
          ref="inputStreetNumber"
          :lang="lang"
          name="streetNumber"
          id="streetNumber"
          v-model="streetNumber"
          :value="streetNumber"
          type="number"
          :label="t('coverageForm.streetNumber.label')"
          :placeholder="t('coverageForm.streetNumber.placeholder')"
          :required="true"
          :disabled="isEmptyValue(street) || snChecked"
          :customLabelClass="
            theme === 'danger'
              ? '!text-danger'
              : isEmptyValue(street) || snChecked
              ? '!text-gray'
              : ''
          "
          :customInputClass="
            theme === 'danger'
              ? '!border-danger'
              : isEmptyValue(street) || snChecked
              ? '!placeholder-gray'
              : 'border-primary'
          "
          :inputError="inputStreetNumberError"
          @input="watchInputStreetNumberValue"
        />
        <div class="absolute inset-y-0 right-0 flex items-start pr-3">
          <label for="snCheckbox" class="mr-2 mt-2 text-sm text-gray">S/N</label>
          <input
            type="checkbox"
            id="snCheckbox"
            v-model="snChecked"
            class="h-4 w-4 mt-2 border-gray"
            @change="onSNCheckedChange"
            :disabled="isEmptyValue(street)"
          />
        </div>
      </div>
      <PwInputText
        ref="inputPostalCode"
        :lang="lang"
        name="zip"
        id="zip"
        v-model="zip"
        :value="zip"
        type="number"
        :label="t('coverageForm.postalCode.label')"
        :placeholder="t('coverageForm.postalCode.placeholder')"
        :required="true"
        :disabled="(zipIsDisabled && !snChecked) || (!snChecked && isEmptyValue(streetNumber))"
        :inputError="inputPostalCodeError"
        :customLabelClass="
          theme === 'danger'
            ? '!text-danger'
            : (zipIsDisabled && !snChecked) || (!snChecked && isEmptyValue(streetNumber))
            ? '!text-gray'
            : ''
        "
        :customInputClass="
          theme === 'danger'
            ? '!border-danger'
            : (zipIsDisabled && !snChecked) || (!snChecked && isEmptyValue(streetNumber))
            ? '!placeholder-gray'
            : 'border-primary'
        "
        class="w-full pl-3"
      />
    </div>
  </div>
  <div
    v-if="
      !snChecked &&
      (!showNotFoundAddressInput || (showNotFoundAddressInput && webcomponent !== 'streetMap')) &&
      showAdditionalInputs
    "
    class="grid gap-4 grid-cols-2 md:grid-cols-8 mt-2"
  >
    <PwSelectAutocomplete
      ref="inputBis"
      :lang="lang"
      :label="t('coverageForm.bis.label')"
      :placeholder="t('coverageForm.bis.placeholder')"
      name="bis"
      id="bis"
      :items="currentBis"
      v-model="bis"
      :value="bis?.bis"
      item-title="bis"
      item-value="bis"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        currentBis.length == 0
      "
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            currentBis.length == 0
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            currentBis.length == 0
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputBisError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputBisValue"
    />
    <PwSelectAutocomplete
      ref="inputBlock"
      :lang="lang"
      :label="t('coverageForm.block.label')"
      :placeholder="t('coverageForm.block.placeholder')"
      name="block"
      id="block"
      :items="currentBlocks"
      v-model="block"
      :value="block?.block"
      item-title="block"
      item-value="block"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        currentBlocks.length == 0
      "
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            currentBlocks.length == 0
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            currentBlocks.length == 0
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputBlockError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputBlockValue"
    />
    <PwSelectAutocomplete
      ref="inputGate"
      :lang="lang"
      :label="t('coverageForm.gate.label')"
      :placeholder="t('coverageForm.gate.placeholder')"
      name="gate"
      id="gate"
      :items="currentGates"
      v-model="gate"
      :value="gate?.gate"
      item-title="gate"
      item-value="gate"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        isEmptyValue(currentGates) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        (!isEmptyValue(currentBlocks) && block == null)
      "
      :inputError="inputGateError"
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            isEmptyValue(currentGates) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null)
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            isEmptyValue(currentGates) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null)
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :classSelectorHeight="'max-h-28'"
      @input="watchInputGateValue"
    />
    <PwSelectAutocomplete
      ref="inputLetter"
      :lang="lang"
      :label="t('coverageForm.letter.label')"
      :placeholder="t('coverageForm.letter.placeholder')"
      name="letter"
      id="letter"
      :items="currentLetters"
      v-model="letter"
      :value="letter?.letter"
      item-title="letter"
      item-value="letter"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        isEmptyValue(currentLetters) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        (!isEmptyValue(currentBlocks) && block == null) ||
        (!isEmptyValue(currentGates) && gates == null)
      "
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            isEmptyValue(currentLetters) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null)
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            isEmptyValue(currentLetters) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null)
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputLetterError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputLetterValue"
    />
    <PwSelectAutocomplete
      ref="inputStair"
      :lang="lang"
      :label="t('coverageForm.stair.label')"
      :placeholder="t('coverageForm.stair.placeholder')"
      name="stair"
      id="stair"
      :items="currentStairs"
      v-model="stair"
      :value="stair?.stair"
      item-title="stair"
      item-value="stair"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        isEmptyValue(currentStairs) ||
        (!isEmptyValue(currentLetters) && letter == null) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        (!isEmptyValue(currentBlocks) && block == null) ||
        (!isEmptyValue(currentGates) && gates == null)
      "
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            isEmptyValue(currentStairs) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null)
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            isEmptyValue(currentStairs) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null)
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputStairError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputStairValue"
    />
    <PwSelectAutocomplete
      ref="inputFloor"
      :lang="lang"
      :label="t('coverageForm.floor.label')"
      :placeholder="t('coverageForm.floor.placeholder')"
      name="floor"
      id="floor"
      :items="currentFloors"
      v-model="floor"
      :value="floor?.floor"
      item-title="floor"
      item-value="floor"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        (isEmptyValue(floor) && isEmptyValue(currentFloors)) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        (!isEmptyValue(currentBlocks) && block == null) ||
        (!isEmptyValue(currentGates) && gates == null) ||
        (!isEmptyValue(currentLetters) && letter == null) ||
        (!isEmptyValue(currentStairs) && stair == null)
      "
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (isEmptyValue(floor) && isEmptyValue(currentFloors)) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentStairs) && stair == null)
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (isEmptyValue(floor) && isEmptyValue(currentFloors)) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentStairs) && stair == null)
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputFloorError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputFloorValue"
    />
    <PwSelectAutocomplete
      ref="inputFirstHand"
      :lang="lang"
      :label="t('coverageForm.firstHand.label')"
      :placeholder="t('coverageForm.firstHand.placeholder')"
      name="firstHand"
      id="firstHand"
      :items="currentFirstHands"
      v-model="firstHand"
      :value="firstHand?.firstHand"
      item-title="firstHand"
      item-value="firstHand"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        (isEmptyValue(firstHand) && isEmptyValue(currentFirstHands)) ||
        (!isEmptyValue(currentBlocks) && block == null) ||
        (!isEmptyValue(currentLetters) && letter == null) ||
        (!isEmptyValue(currentStairs) && stair == null) ||
        (!isEmptyValue(currentFloors) && floor == null) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        (!isEmptyValue(currentGates) && gates == null)
      "
      :customLabelClass="
        theme === 'danger'
          ? '!text-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (isEmptyValue(firstHand) && isEmptyValue(currentFirstHands)) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentStairs) && stair == null) ||
            (!isEmptyValue(currentFloors) && floor == null) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentGates) && gates == null)
          ? '!text-gray'
          : ''
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (isEmptyValue(firstHand) && isEmptyValue(currentFirstHands)) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentStairs) && stair == null) ||
            (!isEmptyValue(currentFloors) && floor == null) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentGates) && gates == null)
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputFirstHandError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputFirstHandValue"
    />
    <PwSelectAutocomplete
      ref="inputSecondHand"
      :lang="lang"
      :label="t('coverageForm.secondHand.label')"
      customLabelClass="opacity-0"
      :placeholder="t('coverageForm.secondHand.placeholder')"
      name="secondHand"
      id="secondHand"
      :items="currentSecondHands"
      v-model="secondHand"
      :value="secondHand?.secondHand"
      item-title="secondHand"
      item-value="secondHand"
      :required="true"
      :disabled="
        isEmptyValue(zip) ||
        (zip && zip.length < 5) ||
        (zip && !numberRegex.test(zip)) ||
        (isEmptyValue(secondHand) && isEmptyValue(currentSecondHands)) ||
        (!isEmptyValue(currentBis) && bis == null) ||
        (!isEmptyValue(currentBlocks) && block == null) ||
        (!isEmptyValue(currentGates) && gates == null) ||
        (!isEmptyValue(currentLetters) && letter == null) ||
        (!isEmptyValue(currentStairs) && stair == null) ||
        (!isEmptyValue(currentFloors) && floor == null) ||
        (!isEmptyValue(currentFirstHands) && firstHand == null)
      "
      :customInputClass="
        theme === 'danger'
          ? '!border-danger'
          : isEmptyValue(zip) ||
            (zip && zip.length < 5) ||
            (zip && !numberRegex.test(zip)) ||
            (isEmptyValue(secondHand) && isEmptyValue(currentSecondHands)) ||
            (!isEmptyValue(currentBis) && bis == null) ||
            (!isEmptyValue(currentBlocks) && block == null) ||
            (!isEmptyValue(currentGates) && gates == null) ||
            (!isEmptyValue(currentLetters) && letter == null) ||
            (!isEmptyValue(currentStairs) && stair == null) ||
            (!isEmptyValue(currentFloors) && floor == null) ||
            (!isEmptyValue(currentFirstHands) && firstHand == null)
          ? '!placeholder-gray'
          : 'border-primary'
      "
      :inputError="inputSecondHandError"
      :classSelectorHeight="'max-h-28'"
      @input="watchInputSecondHandValue"
    />
  </div>
  <div v-if="showNotFoundAddress || showAddress" class="grid gap-4 mb-4 mt-2">
    <p v-if="!showNotFoundAddressInput">
      {{ t('coverageForm.findAddressManually') }}
      <a class="text-primary underline cursor-pointer" @click="onShowNotFoundAddressInput">{{
        t('coverageForm.clickHere')
      }}</a
      >.
    </p>
    <div v-if="showNotFoundAddressInput" class="flex">
      <PwInputText
        ref="inputFullAddress"
        class="w-full"
        :lang="lang"
        name="full_address"
        :label="t('coverageForm.fullAddress.especification')"
        :placeholder="t('coverageForm.fullAddress.especification-dots')"
        :value="getFullAddress()"
        v-model="fullAddress"
        :inputError="inputFullAddressError"
        @input="watchInputFullAddressValue"
        @click="watchInputFullAddressValue"
        :customInputClass="theme === 'danger' ? '!border-danger' : 'border-primary'"
        :custom-label-class="theme === 'danger' ? '!text-danger' : 'text-primary'"
      />
      <div
        @click=";(showNotFoundAddressInput = false), (fullAddress = '')"
        class="-ml-[20px] cursor-pointer w-5 h-5 rounded-full flex items-center justify-center bg-primary-light text-primary hover:text-white hover:bg-primary"
        style="z-index: 101"
      >
        <font-awesome-icon icon="xmark" class="w-3" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'streetMapForm'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { Ref } from 'vue'
import { ref, watch } from 'vue'
import {
  PwInputText,
  PwSelectAutocomplete,
  filterArrayByStringValue,
  padStart,
  isEmptyValue
} from 'parlem-webcomponents-common'
import listFilteredObjectsValue from '@/utils/list-filtered-objects-value' /* (Per fer prober, el funcional està al common) */
import { useI18n } from 'vue-i18n'
import {
  buildingsService,
  locationsService,
  streetsService,
  townsService,
  unitsService
} from '@/services'
import { SET_COVERAGE } from '@/store/constants/store.constants'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import type { IConfigData } from '@/interfaces/config-data.interface'

const store: Store<any> = useStore()
const props = defineProps({
  config: String,
  data: String,
  resetAddress: Number,
  isSaving: Number,
  coverageValidation: Number,
  webcomponent: String,
  showAddress: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['validation'])

const configData: IConfigData = JSON.parse(props.config || '{"lang":"ca","company":"Parlem"}')
const recoveryData: ICoverage = JSON.parse(props.data || '{}')
const lang: string | undefined = configData.lang as string
const theme: string = (configData.theme as string) || 'primary'
const from: string = (configData.from as string) || ''
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
const coverageMessage = t('coverageForm.coverageMessage')
const fullCoverageMessage = t('coverageForm.fullCoverageMessage')
const withoutCoverageMessage = t('coverageForm.withoutCoverageMessage')
const streetTypes = [
  { key: 'all', value: t('streetType.all') },
  { key: 'street', value: t('streetType.street') },
  { key: 'road', value: t('streetType.road') },
  { key: 'highway', value: t('streetType.highway') },
  { key: 'roundabout', value: t('streetType.roundabout') },
  { key: 'passage', value: t('streetType.passage') },
  { key: 'square', value: t('streetType.square') },
  { key: 'promenade', value: t('streetType.promenade') },
  { key: 'bulevar', value: t('streetType.bulevar') },
  { key: 'patrol', value: t('streetType.patrol') },
  { key: 'sector', value: t('streetType.sector') },
  { key: 'urbanization', value: t('streetType.urbanization') },
  { key: 'avenue', value: t('streetType.avenue') },
  { key: 'neighborhood', value: t('streetType.neighborhood') },
  { key: 'gardens', value: t('streetType.gardens') },
  { key: 'park', value: t('streetType.park') },
  { key: 'placette', value: t('streetType.placette') },
  { key: 'settlement', value: t('streetType.settlement') },
  { key: 'track', value: t('streetType.track') },
  { key: 'crossing', value: t('streetType.crossing') },
  { key: 'polygon', value: t('streetType.polygon') },
  { key: 'suburb', value: t('streetType.suburb') }
]

const translatedStreetTypes: any = {
  all: { en: 'All', es: 'Todas', ca: 'Totes' },
  avenue: { en: 'Avenue', es: 'Avenida', ca: 'Avinguda' },
  bulevar: { en: 'Bulevar', es: 'Rambla', ca: 'Rambla' },
  crossing: { en: 'Crossing', es: 'Travesía', ca: 'Travessera' },
  gardens: { en: 'Gardens', es: 'Jardines', ca: 'Jardins' },
  highway: { en: 'Highway', es: 'Carretera', ca: 'Carretera' },
  neighborhood: { en: 'Neighborhood', es: 'Barrio', ca: 'Barri' },
  passage: { en: 'Passage', es: 'Pasaje', ca: 'Passatge' },
  park: { en: 'Park', es: 'Parque', ca: 'Parc' },
  patrol: { en: 'Patrol', es: 'Ronda', ca: 'Ronda' },
  placette: { en: 'Placette', es: 'Placeta', ca: 'Placeta' },
  polygon: { en: 'Polygon', es: 'Polígono', ca: 'Polígon' },
  promenade: { en: 'Promenade', es: 'Paseo', ca: 'Passeig' },
  road: { en: 'Road', es: 'Camino', ca: 'Camí' },
  roundabout: { en: 'Roundabout', es: 'Glorieta', ca: 'Glorieta' },
  sector: { en: 'Sector', es: 'Sector', ca: 'Sector' },
  settlement: { en: 'Settlement', es: 'Poblado', ca: 'Poblat' },
  square: { en: 'Square', es: 'Plaza', ca: 'Plaça' },
  street: { en: 'Street', es: 'Calle', ca: 'Carrer' },
  suburb: { en: 'Suburb', es: 'Colonia', ca: 'Colònia' },
  track: { en: 'Track', es: 'Vía', ca: 'Vía' },
  urbanization: { en: 'Urbanization', es: 'Urbanización', ca: 'Urbanizació' }
}
const inputTown = ref<any>(null)
const inputStreetType = ref<any>(null)
const inputStreet = ref<any>(null)
const inputStreetNumber = ref<any>(null)
const inputPostalCode = ref<any>(null)
const inputBis = ref<any>(null)
const inputBlock = ref<any>(null)
const inputGate = ref<any>(null)
const inputStair = ref<any>(null)
const inputLetter = ref<any>(null)
const inputFloor = ref<any>(null)
const inputFirstHand: any = ref<any>(null)
const inputSecondHand: any = ref<any>(null)
const inputFullAddress = ref<any>(null)
const showAdditionalInputs = ref(true)
let successMessage = ref('')
let towns = ref<any[]>([])
let town: any = ref(null)
let streetType: any = ref(null)
let streets = ref<any[]>([])
let street: any = ref(null)
let gescal7s = ref<string[]>([])
let gescal7 = ref<string>('')
let gescal12: Ref<string> = ref<string>('')
let gescal17: Ref<string> = ref<string>('')
let gescal37: Ref<string> = ref('')
let humanSpecification: Ref<string> = ref('')
let gescal37Possibilities: Ref<string[]> = ref([])
let buildings = ref<string[]>([])
let location: Ref<string | undefined> = ref<string | undefined>('')
let streetNumber: Ref<string | undefined> = ref<string | undefined>('')
let zip: Ref<string | undefined> = ref<string | undefined>('')
let currentBlocks = ref<any[]>([])
let block: any = ref(null)
let blocks = ref<any[]>([])
let currentBis = ref<any[]>([])
let bis: any = ref(null)
let bises = ref<any[]>([])
let currentGates = ref<any[]>([])
let gate: any = ref(null)
let gates = ref<any[]>([])
let units = ref<any[]>([])
let building = ref<any>({})
let stairs = ref<any[]>([])
let letters = ref<any[]>([])
let floors = ref<any[]>([])
let stair: any = ref(null)
let letter: any = ref(null)
let currentStairs = ref<any[]>([])
let currentLetters = ref<any[]>([])
let floor: any = ref(null)
let currentFloors = ref<any[]>([])
let firstHand: any = ref(null)
let firstHands = ref<any[]>([])
let currentFirstHands = ref<any[]>([])
let secondHand: any = ref(null)
let secondHands = ref<any[]>([])
let currentSecondHands = ref<any[]>([])
let showNotFoundAddress = ref(false)
let showNotFoundAddressInput = ref(false)
let snChecked = ref(false)
let inputTownError: Ref<string> = ref<string>('')
let inputStreetTypeError: Ref<string> = ref<string>('')
let inputStreetError: Ref<string> = ref<string>('')
let inputStreetNumberError: Ref<string> = ref<string>('')
let inputPostalCodeError: Ref<string> = ref<string>('')
let inputBlockError: Ref<string> = ref<string>('')
let inputBisError: Ref<string> = ref<string>('')
let inputGateError: Ref<string> = ref<string>('')
let inputStairError: Ref<string> = ref<string>('')
let inputLetterError: Ref<string> = ref<string>('')
let inputFloorError: Ref<string> = ref<string>('')
let inputFirstHandError: Ref<string> = ref<string>('')
let inputSecondHandError: Ref<string> = ref<string>('')
let inputFullAddressError: Ref<string> = ref<string>('')
let fullAddress: Ref<string> = ref<string>('')
let recoveringTown: Ref<boolean> = ref<boolean>(false)
let zipIsDisabled: Ref<boolean> = ref<boolean>(false)
let recoveringStreetType: Ref<boolean> = ref<boolean>(false)
let recoveringStreet: Ref<boolean> = ref<boolean>(false)
let recoveringStreetNumber: Ref<boolean> = ref<boolean>(false)
let recoveringPostalCode: Ref<boolean> = ref<boolean>(false)
let recoveringBlock: Ref<boolean> = ref<boolean>(false)
let recoveringBis: Ref<boolean> = ref<boolean>(false)
let recoveringGate: Ref<boolean> = ref<boolean>(false)
let recoveringStair: Ref<boolean> = ref<boolean>(false)
let recoveringLetter: Ref<boolean> = ref<boolean>(false)
let recoveringFloor: Ref<boolean> = ref<boolean>(false)
let recoveringFirstHand: Ref<boolean> = ref<boolean>(false)
let recoveringSecondHand: Ref<boolean> = ref<boolean>(false)
const numberRegex = /^[0-9]*$/

function setNextInputs(initInput: string) {
  const allInputs = [
    { key: 'town', value: town, input: inputTown.value },
    { key: 'streetType', value: streetType, input: inputStreetType.value },
    { key: 'street', value: street, input: inputStreet.value },
    { key: 'streetNumber', value: streetNumber, input: inputStreetNumber.value },
    { key: 'zip', value: zip, input: inputPostalCode.value },
    { key: 'bis', value: bis, input: inputBis.value },
    { key: 'block', value: block, input: inputBlock.value },
    { key: 'gate', value: gate, input: inputGate.value },
    { key: 'letter', value: letter, input: inputLetter.value },
    { key: 'stair', value: stair, input: inputStair.value },
    { key: 'floor', value: floor, input: inputFloor.value },
    { key: 'firstHand', value: firstHand, input: inputFirstHand.value },
    { key: 'secondHand', value: secondHand, input: inputSecondHand.value }
  ]
  const index = allInputs.findIndex((input) => input.key === initInput)
  const nextInputsValue = allInputs.slice(index + 1)
  return nextInputsValue
}

const inputs = {
  all: {
    resetNext: () => {
      const nextInputs = setNextInputs('all')
      resetValues('all')
      resetInputs(nextInputs)
      snChecked.value = false
      showAdditionalInputs.value = true
    }
  },
  town: {
    resetNext: () => {
      const nextInputs = setNextInputs('town')
      resetValues('town')
      resetInputs(nextInputs)

      if (inputStreetType.value) {
        inputStreetType.value.inputValue = streetTypes[0].value
      }
    }
  },
  streetType: {
    resetNext: () => {
      const nextInputs = setNextInputs('streetType')
      resetValues('streetType')
      resetInputs(nextInputs)
    }
  },
  street: {
    resetNext: () => {
      const nextInputs = setNextInputs('street')
      resetValues('street')
      resetInputs(nextInputs)
    }
  },
  streetNumber: {
    resetNext: () => {
      const nextInputs = setNextInputs('streetNumber')
      resetValues('streetNumber')
      resetInputs(nextInputs)
    }
  },
  bis: {
    resetNext: () => {
      const nextInputs = setNextInputs('bis')
      resetValues('bis')
      resetInputs(nextInputs)
    }
  },
  block: {
    resetNext: () => {
      const nextInputs = setNextInputs('block')
      resetValues('block')
      resetInputs(nextInputs)
    }
  },
  gate: {
    resetNext: () => {
      const nextInputs = setNextInputs('gate')
      resetValues('gate')
      resetInputs(nextInputs)
    }
  },
  letter: {
    resetNext: () => {
      const nextInputs = setNextInputs('letter')
      resetValues('letter')
      resetInputs(nextInputs)
    }
  },
  stair: {
    resetNext: () => {
      const nextInputs = setNextInputs('stair')
      resetValues('stair')
      resetInputs(nextInputs)
    }
  },
  floor: {
    resetNext: () => {
      const nextInputs = setNextInputs('floor')
      resetValues('floor')
      resetInputs(nextInputs)
    }
  },
  firstHand: {
    resetNext: () => {
      const nextInputs = setNextInputs('firstHand')
      resetValues('firstHand')
      resetInputs(nextInputs)
    }
  },
  secondHand: {
    resetNext: () => {
      resetValues('secondHand')
    }
  },
  fullAddress: {
    resetNext: () => {
      resetValues('fullAddress')
    }
  }
}

function resetErrors() {
  inputTownError.value = ''
  inputStreetTypeError.value = ''
  inputStreetError.value = ''
  inputStreetNumberError.value = ''
  inputPostalCodeError.value = ''
  inputBisError.value = ''
  inputBlockError.value = ''
  inputGateError.value = ''
  inputLetterError.value = ''
  inputStairError.value = ''
  inputFloorError.value = ''
  inputFirstHandError.value = ''
  inputSecondHandError.value = ''
  inputFullAddressError.value = ''
}

function resetInputs(nextInputs: any[]) {
  for (const nextInput of nextInputs) {
    if (nextInput.input?.inputValue?.length) {
      nextInput.value.value = null
      nextInput.input.inputValue = ''
    }
  }
}

function resetValues(currentInput: string) {
  resetErrors()

  if (['all'].includes(currentInput)) {
    towns.value = []
    fullAddress.value = ''
    gescal37.value = ''
    humanSpecification.value = ''
  }

  if (['all', 'town'].includes(currentInput)) {
    towns.value = []
    gescal7s.value = []
    gescal7.value = ''
    showNotFoundAddress.value = false
    fullAddress.value = ''
    humanSpecification.value = ''
  }
  if (['all', 'town', 'streetType'].includes(currentInput)) {
    streets.value = []
    fullAddress.value = ''
    humanSpecification.value = ''
  }
  if (['all', 'town', 'streetType', 'street'].includes(currentInput)) {
    gescal12.value = ''
    fullAddress.value = ''
    zip.value = ''
    humanSpecification.value = ''
  }
  if (['all', 'town', 'streetType', 'street', 'streetNumber'].includes(currentInput)) {
    buildings.value = []
    units.value = []
    currentBis.value = []
    currentBlocks.value = []
    currentGates.value = []
    currentStairs.value = []
    currentLetters.value = []
    currentFloors.value = []
    currentFirstHands.value = []
    currentSecondHands.value = []
    fullAddress.value = ''
    gescal37.value = ''
    humanSpecification.value = ''
    zip.value = ''
  }
  if (['all', 'town', 'streetType', 'street', 'streetNumber', 'zip'].includes(currentInput)) {
    buildings.value = []
    units.value = []
    currentBis.value = []
    currentBlocks.value = []
    currentGates.value = []
    currentStairs.value = []
    currentLetters.value = []
    currentFloors.value = []
    currentFirstHands.value = []
    currentSecondHands.value = []
    fullAddress.value = ''
    humanSpecification.value = ''
  }
  if (
    [
      'all',
      'town',
      'streetType',
      'street',
      'streetNumber',
      'bis',
      'block',
      'gate',
      'letter',
      'stair',
      'floor',
      'firstHand',
      'secondHand'
    ].includes(currentInput)
  ) {
    showNotFoundAddressInput.value = false
    fullAddress.value = ''
    humanSpecification.value = ''
  }

  if (['all', 'town', 'streetType', 'street', 'streetNumber', 'bis'].includes(currentInput)) {
    blocks.value = []
  }
  if (
    ['all', 'town', 'streetType', 'street', 'streetNumber', 'bis', 'block'].includes(currentInput)
  ) {
    gates.value = []
  }
  if (
    ['all', 'town', 'streetType', 'street', 'streetNumber', 'bis', 'block', 'gate'].includes(
      currentInput
    )
  ) {
    letters.value = []
  }
  if (
    [
      'all',
      'town',
      'streetType',
      'street',
      'streetNumber',
      'bis',
      'block',
      'gate',
      'letter'
    ].includes(currentInput)
  ) {
    stairs.value = []
  }
  if (
    [
      'all',
      'town',
      'streetType',
      'street',
      'streetNumber',
      'bis',
      'block',
      'gate',
      'letter',
      'stair'
    ].includes(currentInput)
  ) {
    floors.value = []
  }
  if (
    [
      'all',
      'town',
      'streetType',
      'street',
      'streetNumber',
      'bis',
      'block',
      'gate',
      'letter',
      'stair',
      'floor'
    ].includes(currentInput)
  ) {
    firstHands.value = []
    fullAddress.value = ''
  }
  if (
    [
      'all',
      'town',
      'streetType',
      'street',
      'streetNumber',
      'bis',
      'block',
      'gate',
      'letter',
      'stair',
      'floor',
      'firstHand'
    ].includes(currentInput)
  ) {
    secondHands.value = []
  }
}

function reset() {
  inputs.all.resetNext()
}

function getCoverageData(): ICoverage {
  const coverageData: ICoverage = {
    town: inputTown.value?.inputValue,
    ineCode: town.value?.ineCode,
    zip: inputPostalCode.value?.inputValue || zip.value,
    streetType: street.value?.streetType || streetType.value?.key,
    street: street.value?.streetName,
    location: street.value?.location,
    streetNumber: snChecked.value ? 's/n' : inputStreetNumber.value?.inputValue,
    bis: checkValue(inputBis.value?.inputValue),
    block: checkValue(inputBlock.value?.inputValue),
    gate: checkValue(inputGate.value?.inputValue),
    stair: checkValue(inputStair.value?.inputValue),
    letter: checkValue(inputLetter.value?.inputValue),
    floor: checkValue(inputFloor.value?.inputValue),
    firstHand: checkValue(inputFirstHand.value?.inputValue),
    secondHand: checkValue(inputSecondHand.value?.inputValue),
    fullAddress: fullAddress.value, //inputFullAddress.value?.inputValue,
    gescal7: gescal7.value,
    gescal12: gescal12.value,
    gescal17: getGescal17(gescal12.value, inputStreetNumber.value?.inputValue),
    gescal37: gescal37.value,
    humanSpecification:
      showNotFoundAddressInput.value && props.webcomponent === 'streetMap'
        ? fullAddress.value
        : humanSpecification.value,
    from: from
  }
  if (gescal37Possibilities.value.length) {
    coverageData.gescal37Possibilities = gescal37Possibilities.value
  }

  store.dispatch(SET_COVERAGE, coverageData)
  return coverageData
}

function checkValue(value: string): string {
  return value === '-' ? '' : value
}

function showFormInputErrors(): void {
  if (
    !isEmptyValue(inputFullAddressError.value) ||
    !isEmptyValue(inputFullAddress.value?.inputValue)
  ) {
    return
  }

  if (!isEmptyValue(inputFullAddress.value) && isEmptyValue(inputFullAddress.value.inputValue)) {
    inputFullAddressError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (isEmptyValue(inputTown.value.inputValue) || isEmptyValue(town.value)) {
    inputTownError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputTownError.value)) {
    return
  }

  if (isEmptyValue(inputStreet.value.inputValue) || isEmptyValue(street.value)) {
    inputStreetError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputStreetError.value)) {
    return
  }
  if (
    isEmptyValue(inputStreetNumber.value.inputValue) ||
    isEmptyValue(streetNumber.value) ||
    (isEmptyValue(units.value) && isEmptyValue(building.value))
  ) {
    inputStreetNumberError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputStreetNumberError.value)) {
    return
  }

  if (
    inputPostalCode.value.inputValue === 'null' ||
    inputPostalCode.value.inputValue === 'VARIO' ||
    !numberRegex.test(inputPostalCode.value.inputValue) ||
    inputPostalCode.value.inputValue.length < 5
  ) {
    inputPostalCodeError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputPostalCodeError.value)) {
    return
  }
  if (
    !isEmptyValue(bises.value) &&
    (isEmptyValue(inputBis.value.inputValue) ||
      (isEmptyValue(bis.value) && inputBis.value.inputValue !== '-'))
  ) {
    inputBisError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputBisError.value)) {
    return
  }
  if (
    !isEmptyValue(blocks.value) &&
    (isEmptyValue(inputBlock.value.inputValue) ||
      (isEmptyValue(block.value) && inputBlock.value.inputValue !== '-'))
  ) {
    inputBlockError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputBlockError.value)) {
    return
  }
  if (
    !isEmptyValue(gates.value) &&
    (isEmptyValue(inputGate.value.inputValue) ||
      (isEmptyValue(gate.value) && inputGate.value.inputValue !== '-'))
  ) {
    inputGateError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputGateError.value)) {
    return
  }
  if (
    !isEmptyValue(letters.value) &&
    (isEmptyValue(inputLetter.value.inputValue) ||
      (isEmptyValue(letter.value) && inputLetter.value.inputValue !== '-'))
  ) {
    inputLetterError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputLetterError.value)) {
    return
  }

  if (
    !isEmptyValue(stairs.value) &&
    (isEmptyValue(inputStair.value.inputValue) ||
      (isEmptyValue(stair.value) && inputStair.value.inputValue !== '-'))
  ) {
    inputStairError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputStairError.value)) {
    return
  }

  if (
    !isEmptyValue(floors.value) &&
    (isEmptyValue(inputFloor.value.inputValue) ||
      (isEmptyValue(floor.value) && inputFloor.value.inputValue !== '-'))
  ) {
    inputFloorError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputFloorError.value)) {
    return
  }

  if (
    !isEmptyValue(firstHands.value) &&
    (isEmptyValue(inputFirstHand.value.inputValue) ||
      (isEmptyValue(firstHand.value) && inputFirstHand.value.inputValue !== '-'))
  ) {
    inputFirstHandError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputFirstHandError.value)) {
    return
  }

  if (
    !isEmptyValue(secondHands.value) &&
    (isEmptyValue(inputSecondHand.value.inputValue) ||
      (isEmptyValue(secondHand.value) && inputSecondHand.value.inputValue !== '-'))
  ) {
    inputSecondHandError.value = t('coverageForm.invalidInputValue')
    return
  }

  if (!isEmptyValue(inputSecondHandError.value)) {
    return
  }
}

const isValidForm = (): boolean => {
  if (snChecked.value && isEmptyValue(inputFullAddress.value?.inputValue)) {
    return false
  }
  if (
    inputStreetNumberError.value === t('coverageForm.inputErrorNumber') &&
    isEmptyValue(inputFullAddress.value?.inputValue)
  ) {
    return false
  }
  if (
    !isEmptyValue(inputFullAddress.value?.inputValue) &&
    !isEmptyValue(inputPostalCodeError.value)
  ) {
    return true
  }
  if (
    !isEmptyValue(inputFullAddress.value?.inputValue) &&
    showNotFoundAddressInput &&
    !isEmptyValue(inputPostalCodeError.value)
  ) {
    return true
  }
  if (
    !isEmptyValue(inputSecondHand.value?.inputValue) &&
    isEmptyValue(inputSecondHandError.value)
  ) {
    return true
  } else if (!isEmptyValue(secondHands.value) || !isEmptyValue(inputSecondHandError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputFirstHand.value?.inputValue) &&
    isEmptyValue(inputFirstHandError.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(firstHands.value) || !isEmptyValue(inputFirstHandError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputFloor.value?.inputValue) &&
    isEmptyValue(inputFloorError.value) &&
    isEmptyValue(currentFirstHands.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(floors.value) || !isEmptyValue(inputFloorError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputStair.value?.inputValue) &&
    isEmptyValue(inputStairError.value) &&
    isEmptyValue(currentFloors.value) &&
    isEmptyValue(currentFirstHands.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(stairs.value) || !isEmptyValue(inputStairError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputLetter.value?.inputValue) &&
    isEmptyValue(inputLetterError.value) &&
    isEmptyValue(currentStairs.value) &&
    isEmptyValue(currentFloors.value) &&
    isEmptyValue(currentFirstHands.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(letters.value) || !isEmptyValue(inputLetterError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputGate.value?.inputValue) &&
    isEmptyValue(inputGateError.value) &&
    isEmptyValue(currentLetters.value) &&
    isEmptyValue(currentStairs.value) &&
    isEmptyValue(currentFloors.value) &&
    isEmptyValue(currentFirstHands.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(gates.value) || !isEmptyValue(inputGateError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputBlock.value?.inputValue) &&
    isEmptyValue(inputBlockError.value) &&
    isEmptyValue(currentGates.value) &&
    isEmptyValue(currentLetters.value) &&
    isEmptyValue(currentStairs.value) &&
    isEmptyValue(currentFloors.value) &&
    isEmptyValue(currentFirstHands.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(blocks.value) || !isEmptyValue(inputBlockError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputBis.value?.inputValue) &&
    isEmptyValue(inputBisError.value) &&
    isEmptyValue(currentBlocks.value) &&
    isEmptyValue(currentGates.value) &&
    isEmptyValue(currentLetters.value) &&
    isEmptyValue(currentStairs.value) &&
    isEmptyValue(currentFloors.value) &&
    isEmptyValue(currentFirstHands.value) &&
    isEmptyValue(currentSecondHands.value)
  ) {
    return true
  } else if (!isEmptyValue(bises.value) || !isEmptyValue(inputBisError.value)) {
    return false
  }
  if (
    !isEmptyValue(inputPostalCode.value?.inputValue) &&
    isEmptyValue(inputPostalCodeError.value) &&
    inputPostalCode.value?.inputValue !== 'null' &&
    inputPostalCode.value?.inputValue !== 'VARIO' &&
    numberRegex.test(inputPostalCode.value.inputValue) &&
    inputPostalCode.value?.inputValue?.length >= 5
  ) {
    return true
  } else if (!isEmptyValue(inputPostalCodeError.value)) {
    return false
  }
  return false
}

watch(isValidForm, (isValid) => {
  getFullAddress()
  getCoverageData()
  emit('validation', isValid)
})

function getFullAddress() {
  if (snChecked.value && !fullAddress.value) {
    getCoverageData()
    return ''
  }

  if (fullAddress.value && fullAddress.value.length) {
    getCoverageData()
    return fullAddress.value
  }

  const townValue = town.value
  const streetValue = street.value
  const streetNumberValue = streetNumber.value
  const zipValue = zip.value
  const bisValue = bis.value
  const blockValue = block.value
  const gateValue = gate.value
  const letterValue = letter.value
  const stairValue = stair.value
  const floorValue = floor.value
  const firstHandValue = firstHand.value
  const secondHandValue = secondHand.value

  const fullStreetName = [streetValue?.streetType, streetValue?.streetName, streetNumberValue].join(
    ' '
  )
  const resultAddress = `${[
    fullStreetName,
    bisValue,
    blockValue,
    gateValue,
    letterValue,
    stairValue,
    floorValue?.floor,
    firstHandValue,
    secondHandValue,
    townValue?.name
  ]
    .filter((item) => item && item.length > 0)
    .join(', ')}${zipValue ? ' (' + zipValue + ')' : ''}`

  getCoverageData()
  return ''
}

const getGescal17 = (gescal12: string, streetNumber: string): string => {
  const validStreetNumber = streetNumber != '' ? streetNumber : '99999'
  return gescal12 + padStart(validStreetNumber, 5, '0')
}

async function setTowns(address: string) {
  await townsService
    .cancel('Re-executed call')
    .get(address)
    .then((townResult: any) => {
      if (townResult) {
        townResult = townResult.map((townData: any) => {
          return {
            ...townData,
            fullTownName: `${townData.name} (${townData.province})`
          }
        })
        towns.value = townResult
        inputTownError.value = ''
      }
    })
    .catch((error: any) => {
      if (error.name != 'CanceledError') {
        inputTownError.value = t('coverageForm.inputError')
        console.log(error)
      }
    })
}

function watchInputTownValue(event: any) {
  if (town.value) {
    town.value = null
  }
  const inputValue = event.target.value

  if (inputValue.length >= 3) {
    setTowns(inputValue)
  }
}

async function setGescal7(address: string, ineCode: string) {
  address.length &&
    ineCode.length &&
    (await locationsService
      .cancel('Re-executed call')
      .get(ineCode)
      .then((locationResponse: any) => {
        inputTownError.value = ''
        gescal7s.value = locationResponse?.map((location: any) => location.gescal7) || []

        if (gescal7s.value.length == 0) {
          successMessage.value = withoutCoverageMessage
          return
        }

        gescal7.value = gescal7s.value[0]
      })
      .catch((error: any) => {
        if (error.name != 'CanceledError') {
          inputTownError.value = t('coverageForm.inputError')
        }
      }))
}
watch(
  () => props.resetAddress,
  (newIndex, oldIndex) => {
    if (newIndex !== oldIndex) {
      reset()
    }
  }
)
watch(
  () => props.coverageValidation,
  (newIndex, oldIndex) => {
    if (newIndex !== oldIndex) {
      resetErrors()
      showFormInputErrors()
      isValidForm()
      getCoverageData()
    }
  }
)

watch(town, (currentTownValue: any) => {
  if (recoveringTown.value) {
    recoveringTown.value = false
    return
  }

  const ineCode = currentTownValue?.ineCode
  const townData: any = towns.value.find((town) => town.ineCode === ineCode)
  const address = townData?.name || ''

  inputs.town.resetNext()

  if (isEmptyValue(currentTownValue)) {
    return
  }
  setGescal7(address, ineCode)
})

function watchInputStreetTypeValue(event: any) {
  if (streetType.value) {
    streetType.value = null
  }
}

watch(streetType, (currentStreetTypeValue: any) => {
  if (recoveringStreetType.value) {
    recoveringStreetType.value = false
    return
  }

  inputs.streetType.resetNext()

  if (isEmptyValue(currentStreetTypeValue)) {
    return
  }
})

async function setInputStreetValue(inputValue: string) {
  //showNotFoundAddress.value = true

  showNotFoundAddressInput.value = false

  const streetTypeValue: any = streetType.value
  const streetTypeAllLanguages: Array<string> = []

  if (!isEmptyValue(streetTypeValue) && streetTypeValue.key != 'all') {
    streetTypeAllLanguages.push(
      translatedStreetTypes[streetTypeValue.key].en.toLowerCase(),
      translatedStreetTypes[streetTypeValue.key].es.toLowerCase(),
      translatedStreetTypes[streetTypeValue.key].ca.toLowerCase()
    )
  }

  await streetsService
    .cancel('Re-executed call')
    .get(inputValue, gescal7s.value)
    .then((streetResult: any) => {
      if (streetResult) {
        streetResult = streetResult.map((streetData: any) => {
          return {
            ...streetData,
            fullStreetName: `${streetData.streetType} ${streetData.streetName} (${streetData.location})`
          }
        })

        streets.value = streetTypeAllLanguages.length
          ? streetResult.filter((result: any) =>
              streetTypeAllLanguages.includes(result.streetType?.toLowerCase())
            )
          : streetResult
        inputStreetError.value = ''
      }
    })
    .catch((error: any) => {
      if (error.name != 'CanceledError') {
        inputStreetError.value = t('coverageForm.inputError')
        console.log(error)
      }
    })
}

async function watchInputStreetValue(event: any) {
  if (street.value) {
    street.value = null
  }

  const inputValue = event.target.value
  if (inputValue.length >= 2) {
    await setInputStreetValue(inputValue)
  }
}
async function watchInputStreetNumberValue(event: any) {
  const inputValue = event.target.value
  if (inputValue.length > 0 && props.webcomponent !== 'coverage') {
    showNotFoundAddress.value = true
  } else {
    showAdditionalInputs.value = true
  }
}

async function setBuildings() {
  const streetData: any = streets.value.find((street) => street.gescal12 === gescal12.value)
  streetData &&
    (await buildingsService
      .cancel('Re-executed call')
      .get(gescal12.value)
      .then((buildingResponse: any) => {
        buildings.value = buildingResponse.filter(
          (build: any) => build.streetName?.toLowerCase() == streetData.streetName?.toLowerCase()
        )

        if (buildings.value.length == 0) {
          successMessage.value = withoutCoverageMessage
        }

        inputStreetError.value = ''
      })
      .catch((error) => {
        if (error.name != 'CanceledError') {
          inputStreetError.value = t('coverageForm.inputErrorBuilding')
          console.log(error)
        }
      }))
}

watch(street, (currentStreetValue: any) => {
  if (recoveringStreet.value) {
    recoveringStreet.value = false
    return
  }

  inputs.street.resetNext()

  if (isEmptyValue(currentStreetValue)) {
    return
  }
  gescal12.value = currentStreetValue?.gescal12
  // TO DO
  // setBuildings()
})

async function setUnits(gescal17: string) {
  gescal17?.length &&
    (await unitsService
      .cancel('Re-executed call')
      .get(gescal17)
      .then((unitsResponse: any) => {
        inputPostalCode.value.inputValue = unitsResponse.building.zip
        zip.value = unitsResponse.building.zip
        zipIsDisabled.value =
          inputPostalCode.value.inputValue !== 'null' &&
          inputPostalCode.value.inputValue !== '' &&
          inputPostalCode.value.inputValue !== 'VARIO' &&
          numberRegex.test(inputPostalCode.value.inputValue)
        units.value = unitsResponse.items
        building.value = unitsResponse.building
        if (unitsResponse.items.length > 0) {
          bises.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'bis' }],
            propertyData: 'bis'
          })
          blocks.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'block' }],
            propertyData: 'block'
          })
          gates.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'gate' }],
            propertyData: 'gate'
          })
          letters.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'letter' }],
            propertyData: 'letter'
          })
          stairs.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'stair' }],
            propertyData: 'stair'
          })
          floors.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'floor' }],
            propertyData: 'floor'
          })
          firstHands.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'firstHand' }],
            propertyData: 'firstHand'
          })
          secondHands.value = listFilteredObjectsValue({
            items: unitsResponse.items,
            compareData: [{ propertyFilter: 'secondHand' }],
            propertyData: 'secondHand'
          })
          currentBis.value = isEmptyValue(bises.value) ? [] : bises.value
          currentBlocks.value = !isEmptyValue(currentBis.value) ? [] : blocks.value
          currentGates.value = !isEmptyValue(currentBlocks.value) ? [] : gates.value
          currentLetters.value = !isEmptyValue(currentGates.value) ? [] : letters.value
          currentStairs.value = !isEmptyValue(currentLetters.value) ? [] : stairs.value
          currentFloors.value = !isEmptyValue(currentStairs.value) ? [] : floors.value
          successMessage.value = isEmptyValue(unitsResponse.items)
            ? coverageMessage
            : withoutCoverageMessage
          inputStreetNumberError.value = ''
        }
      })
      .catch((error) => {
        if (error.name != 'CanceledError') {
          inputStreetNumberError.value = t('coverageForm.inputErrorNumber')
          showAdditionalInputs.value = false
          showNotFoundAddress.value = true
          zipIsDisabled.value = false
          showNotFoundAddressInput.value = true
        }
      }))
}

watch(streetNumber, (currentStreetNumberValue: any) => {
  if (recoveringStreetNumber.value) {
    recoveringStreetNumber.value = false
    return
  }

  inputs.streetNumber.resetNext()

  if (!currentStreetNumberValue?.length) {
    return
  }

  const gescal17 =
    currentStreetNumberValue?.length && getGescal17(gescal12.value, currentStreetNumberValue)
  setUnits(gescal17)
})

watch(zip, (currentZipValue: any) => {
  if (recoveringPostalCode.value) {
    recoveringPostalCode.value = false
    return
  }
})

function watchInputBisValue(event: any) {
  if (bis.value) {
    bis.value = null
  }

  if (!filterArrayByStringValue(bises.value, inputBis.value.inputValue).length) {
    inputBisError.value = t('coverageForm.inputError')
  }
}

watch(bis, (currentBisValue: any) => {
  if (recoveringBis.value) {
    recoveringBlock.value = false
  } else {
    inputs.bis.resetNext()
  }

  if (isEmptyValue(currentBisValue?.bis)) {
    return
  }

  currentBisValue.bis = currentBisValue.bis === '-' ? '' : currentBisValue.bis
  bis.value = currentBisValue.bis
  const compareData = [
    { propertyFilter: 'bis', comparator: 'eq', compareValue: currentBisValue.bis }
  ]

  blocks.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'block'
  })
  gates.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gate'
  })
  letters.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'letter'
  })
  stairs.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'stair'
  })
  floors.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'floor'
  })
  currentBlocks.value = blocks.value
  currentGates.value = gates.value
  currentLetters.value = letters.value
  currentStairs.value = currentLetters.value.length > 0 ? [] : stairs.value
  currentFloors.value = currentStairs.value.length > 0 ? [] : floors.value

  if (
    blocks.value.length == 0 &&
    gates.value.length == 0 &&
    letters.value.length == 0 &&
    stairs.value.length == 0 &&
    floors.value.length == 0
  ) {
    successMessage.value = fullCoverageMessage
  }
})

function watchInputBlockValue(event: any) {
  if (block.value) {
    block.value = null
  }

  if (
    !filterArrayByStringValue(
      blocks.value.map((item: string) => item),
      inputBlock.value.inputValue
    ).length
  ) {
    inputBlockError.value = t('coverageForm.inputError')
  }
}

watch(block, (currentBlockValue: any) => {
  if (recoveringBlock.value) {
    recoveringBlock.value = false
  } else {
    inputs.block.resetNext()
  }

  if (isEmptyValue(currentBlockValue?.block)) {
    return
  }
  currentBlockValue.block = currentBlockValue.block === '-' ? '' : currentBlockValue.block
  block.value = currentBlockValue.block

  const bisValidatorValue = bis.value
  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.bis || bisValidatorValue || ''
    },
    { propertyFilter: 'block', comparator: 'eq', compareValue: currentBlockValue.block }
  ]
  const blockData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const humanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  if (blockData.length == 1) {
    gescal37.value = blockData[0]
    humanSpecification.value = humanData[0]
    getCoverageData()
  }
  gates.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gate'
  })
  letters.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'letter'
  })
  stairs.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'stair'
  })
  floors.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'floor'
  })
  currentGates.value = gates.value
  currentLetters.value = letters.value
  currentStairs.value = stairs.value
  currentFloors.value = currentStairs.value.length > 0 ? [] : floors.value

  if (
    gates.value.length == 0 &&
    letters.value.length == 0 &&
    stairs.value.length == 0 &&
    floors.value.length == 0
  ) {
    successMessage.value = fullCoverageMessage
  }
})

function watchInputGateValue(event: any) {
  if (gate.value) {
    gate.value = null
  }

  if (
    !filterArrayByStringValue(
      gates.value.map((item: string) => item),
      inputGate.value.inputValue
    ).length
  ) {
    inputGateError.value = t('coverageForm.inputError')
  }
}

watch(gate, (currentGateValue: any) => {
  if (recoveringGate.value) {
    recoveringGate.value = false
  } else {
    inputs.gate.resetNext()
  }

  if (isEmptyValue(currentGateValue?.gate)) {
    return
  }
  currentGateValue.gate = currentGateValue.gate === '-' ? '' : currentGateValue.gate
  gate.value = currentGateValue.gate

  const bisValidatorValue = bis.value
  const blockValidatorValue = block.value
  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.bis || bisValidatorValue || ''
    },
    {
      propertyFilter: 'block',
      comparator: 'eq',
      compareValue: blockValidatorValue?.block || blockValidatorValue || ''
    },
    { propertyFilter: 'gate', comparator: 'eq', compareValue: currentGateValue.gate }
  ]
  const gateData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const humanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  if (gateData.length == 1) {
    gescal37.value = gateData[0]
    humanSpecification.value = humanData[0]
    getCoverageData()
  }
  letters.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'letter'
  })
  stairs.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'stair'
  })
  floors.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'floor'
  })
  currentLetters.value = letters.value
  currentStairs.value = stairs.value
  currentFloors.value = currentStairs.value.length > 0 ? [] : floors.value

  if (letters.value.length == 0 && stairs.value.length == 0 && floors.value.length == 0) {
    successMessage.value = fullCoverageMessage
  }
})

function onSNCheckedChange() {
  if (snChecked.value) {
    inputStreetNumber.value.inputValue = ''
    inputPostalCode.value.inputValue = ''
    showNotFoundAddress.value = true
    showNotFoundAddressInput.value = true
  } else {
    showNotFoundAddress.value = false
    showNotFoundAddressInput.value = false
  }
}

function watchInputLetterValue(event: any) {
  if (letter.value) {
    letter.value = null
  }

  if (
    !filterArrayByStringValue(
      letters.value.map((item: string) => item),
      inputLetter.value.inputValue
    ).length
  ) {
    inputLetterError.value = t('coverageForm.inputError')
  }
}

watch(letter, (currentLetterValue: any) => {
  if (recoveringLetter.value) {
    recoveringLetter.value = false
  } else {
    inputs.letter.resetNext()
  }

  if (isEmptyValue(currentLetterValue?.letter)) {
    return
  }
  currentLetterValue.letter = currentLetterValue.letter === '-' ? '' : currentLetterValue.letter
  letter.value = currentLetterValue.letter

  const blockValidatorValue = block.value
  const bisValidatorValue = bis.value
  const gateValidatorValue = gate.value

  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.block || bisValidatorValue || ''
    },
    {
      propertyFilter: 'block',
      comparator: 'eq',
      compareValue: blockValidatorValue?.block || blockValidatorValue || ''
    },
    {
      propertyFilter: 'gate',
      comparator: 'eq',
      compareValue: gateValidatorValue?.block || gateValidatorValue || ''
    },
    { propertyFilter: 'letter', comparator: 'eq', compareValue: currentLetterValue.letter }
  ]
  const letterData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const humanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  if (letterData.length == 1) {
    gescal37.value = letterData[0]
    humanSpecification.value = humanData[0]
    getCoverageData()
  }
  stairs.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'stair'
  })
  floors.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'floor'
  })
  currentStairs.value = stairs.value
  currentFloors.value = currentStairs.value.length > 0 ? [] : floors.value

  if (floors.value.length == 0 && floors.value.length == 0) {
    successMessage.value = fullCoverageMessage
  }
})

function watchInputStairValue(event: any) {
  if (stair.value) {
    stair.value = null
  }

  if (
    !filterArrayByStringValue(
      stairs.value.map((item: string) => item),
      inputStair.value.inputValue
    ).length
  ) {
    inputStairError.value = t('coverageForm.inputError')
  }
}

watch(stair, (currentStairValue: any) => {
  if (recoveringStair.value) {
    recoveringStair.value = false
  } else {
    inputs.stair.resetNext()
  }

  if (isEmptyValue(currentStairValue?.stair)) {
    return
  }
  currentStairValue.stair = currentStairValue.stair === '-' ? '' : currentStairValue.stair
  stair.value = currentStairValue.stair

  const blockValidatorValue = block.value
  const bisValidatorValue = bis.value
  const gateValidatorValue = gate.value
  const letterValidatorValue = letter.value
  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.block || bisValidatorValue || ''
    },
    {
      propertyFilter: 'block',
      comparator: 'eq',
      compareValue: blockValidatorValue?.block || blockValidatorValue || ''
    },
    {
      propertyFilter: 'gate',
      comparator: 'eq',
      compareValue: gateValidatorValue?.block || gateValidatorValue || ''
    },
    {
      propertyFilter: 'letter',
      comparator: 'eq',
      compareValue: letterValidatorValue?.letter || letterValidatorValue || ''
    },
    { propertyFilter: 'stair', comparator: 'eq', compareValue: currentStairValue.stair }
  ]
  const stairData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const stairHumanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  if (stairData.length == 1) {
    gescal37.value = stairData[0]
    humanSpecification.value = stairHumanData[0]
    getCoverageData()
  }
  floors.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'floor'
  })
  currentFloors.value = floors.value

  if (floors.value.length == 0) {
    successMessage.value = fullCoverageMessage
  }
})

function watchInputFloorValue(event: any) {
  if (floor.value) {
    floor.value = null
  }

  if (
    !filterArrayByStringValue(
      floors.value.map((item: string) => item),
      inputFloor.value.inputValue
    ).length
  ) {
    inputFloorError.value = t('coverageForm.inputError')
  }
}

watch(floor, (currentFloorValue: any, oldFloorValue: any) => {
  if (recoveringFloor.value) {
    recoveringFloor.value = false
  } else {
    inputs.floor.resetNext()
  }

  if (isEmptyValue(currentFloorValue?.floor)) {
    return
  }

  currentFloorValue.floor = currentFloorValue.floor === '-' ? '' : currentFloorValue.floor
  floor.value = currentFloorValue

  const blockValidatorValue = block.value
  const letterValidatorValue = letter.value
  const stairValidatorValue = stair.value
  const bisValidatorValue = bis.value
  const gateValidatorValue = gate.value
  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.block || bisValidatorValue || ''
    },
    {
      propertyFilter: 'block',
      comparator: 'eq',
      compareValue: blockValidatorValue?.block || blockValidatorValue || ''
    },
    {
      propertyFilter: 'gate',
      comparator: 'eq',
      compareValue: gateValidatorValue?.block || gateValidatorValue || ''
    },
    {
      propertyFilter: 'letter',
      comparator: 'eq',
      compareValue: letterValidatorValue?.letter || letterValidatorValue || ''
    },
    {
      propertyFilter: 'stair',
      comparator: 'eq',
      compareValue: stairValidatorValue?.stair || stairValidatorValue || ''
    },
    { propertyFilter: 'floor', comparator: 'eq', compareValue: currentFloorValue.floor }
  ]

  const floorData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const floorHumanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  firstHands.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'firstHand'
  })
  currentFirstHands.value = firstHands.value

  secondHands.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'secondHand'
  })
  currentSecondHands.value = secondHands.value

  if (firstHands.value.length === 0 && secondHands.value.length === 0 && floorData.length) {
    gescal37.value = floorData[0]
    humanSpecification.value = floorHumanData[0]
    gescal37Possibilities.value = floorData.length > 1 ? floorData : []
    successMessage.value = fullCoverageMessage
    getCoverageData()
  }
})

function watchInputFirstHandValue(event: any) {
  if (firstHand.value) {
    firstHand.value = null
  }

  if (
    !filterArrayByStringValue(
      firstHands.value.map((item: any) => item.firstHand),
      inputFirstHand.value.inputValue
    ).length
  ) {
    inputFirstHandError.value = t('coverageForm.inputError')
  }
}

watch(firstHand, (currentFirstHandValue: any) => {
  if (recoveringFirstHand.value) {
    recoveringFirstHand.value = false
  } else {
    inputs.firstHand.resetNext()
  }

  if (isEmptyValue(currentFirstHandValue)) {
    return
  }

  if (currentFirstHandValue.firstHand) {
    currentFirstHandValue.firstHand =
      currentFirstHandValue.firstHand === '-' ? '' : currentFirstHandValue.firstHand
  } else if (currentFirstHandValue && !currentFirstHandValue.firstHand) {
    currentFirstHandValue = {
      firstHand: currentFirstHandValue === '-' ? '' : currentFirstHandValue
    }
  }
  firstHand.value = currentFirstHandValue
  const bisValidatorValue = bis.value
  const gateValidatorValue = gate.value
  const blockValidatorValue = block.value
  const stairValidatorValue = stair.value
  const letterValidatorValue = letter.value
  const floorValidatorValue = floor.value
  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.block || bisValidatorValue || ''
    },
    {
      propertyFilter: 'block',
      comparator: 'eq',
      compareValue: blockValidatorValue?.block || blockValidatorValue || ''
    },
    {
      propertyFilter: 'gate',
      comparator: 'eq',
      compareValue: gateValidatorValue?.block || gateValidatorValue || ''
    },
    {
      propertyFilter: 'letter',
      comparator: 'eq',
      compareValue: letterValidatorValue?.letter || letterValidatorValue || ''
    },
    {
      propertyFilter: 'stair',
      comparator: 'eq',
      compareValue: stairValidatorValue?.letter || stairValidatorValue || ''
    },
    {
      propertyFilter: 'floor',
      comparator: 'eq',
      compareValue: floorValidatorValue?.floor || floorValidatorValue || ''
    },
    {
      propertyFilter: 'firstHand',
      comparator: 'eq',
      compareValue: currentFirstHandValue.firstHand
    }
  ]
  const firstHandData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const firstHandHumanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  secondHands.value = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'secondHand'
  })
  currentSecondHands.value = secondHands.value

  if (secondHands.value.length === 0 && firstHandData.length) {
    successMessage.value = fullCoverageMessage
    gescal37.value = firstHandData[0]
    humanSpecification.value = firstHandHumanData[0]
    gescal37Possibilities.value = firstHandData.length > 1 ? firstHandData : []
    getCoverageData()
  }
})

function watchInputSecondHandValue(event: any) {
  if (secondHand.value) {
    secondHand.value = null
  }

  if (
    !filterArrayByStringValue(
      secondHands.value.map((item: any) => item.secondHand),
      inputSecondHand.value.inputValue
    ).length
  ) {
    inputSecondHandError.value = t('coverageForm.inputError')
  }
}

watch(secondHand, (currentSecondHandValue: any, oldSecondHand) => {
  if (recoveringSecondHand.value) {
    recoveringSecondHand.value = false
  } else {
    inputs.secondHand.resetNext()
  }

  if (isEmptyValue(currentSecondHandValue)) {
    return
  }
  if (currentSecondHandValue.secondHand) {
    currentSecondHandValue.secondHand =
      currentSecondHandValue.secondHand === '-' ? '' : currentSecondHandValue.secondHand
  } else if (currentSecondHandValue && !currentSecondHandValue.secondHand) {
    currentSecondHandValue = {
      secondHand: currentSecondHandValue === '-' ? '' : currentSecondHandValue
    }
  }
  secondHand.value = currentSecondHandValue

  const bisValidatorValue = bis.value
  const blockValidatorValue = block.value
  const gateValidatorValue = gate.value
  const stairValidatorValue = stair.value
  const letterValidatorValue = letter.value
  const floorValidatorValue = floor.value
  const firstHandValidatorValue = firstHand.value
  const compareData = [
    {
      propertyFilter: 'bis',
      comparator: 'eq',
      compareValue: bisValidatorValue?.bis || bisValidatorValue || ''
    },
    {
      propertyFilter: 'block',
      comparator: 'eq',
      compareValue: blockValidatorValue?.block || blockValidatorValue || ''
    },
    {
      propertyFilter: 'gate',
      comparator: 'eq',
      compareValue: gateValidatorValue?.gate || gateValidatorValue || ''
    },
    {
      propertyFilter: 'letter',
      comparator: 'eq',
      compareValue: letterValidatorValue?.letter || letterValidatorValue || ''
    },
    {
      propertyFilter: 'stair',
      comparator: 'eq',
      compareValue: stairValidatorValue?.stair || stairValidatorValue || ''
    },
    {
      propertyFilter: 'floor',
      comparator: 'eq',
      compareValue: floorValidatorValue?.floor || floorValidatorValue || ''
    },
    {
      propertyFilter: 'firstHand',
      comparator: 'eq',
      compareValue: firstHandValidatorValue?.firstHand || firstHandValidatorValue || ''
    },
    {
      propertyFilter: 'secondHand',
      comparator: 'eq',
      compareValue: currentSecondHandValue.secondHand
    }
  ]
  const secondHandData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'gescal37',
    reduce: false,
    trim: false
  })
  const secondHandHumanData = listFilteredObjectsValue({
    items: units.value,
    compareData: compareData,
    propertyData: 'humanSpecification',
    reduce: false,
    trim: false
  })
  if (secondHandData.length) {
    gescal37.value = secondHandData[0]
    humanSpecification.value = secondHandHumanData[0]
    gescal37Possibilities.value = secondHandData.length > 1 ? secondHandData : []
    getCoverageData()
  }
  successMessage.value = fullCoverageMessage
})

function watchInputFullAddressValue(event: any) {
  if (inputFullAddress.value.value != inputFullAddress.value.inputValue) {
    inputs.fullAddress.resetNext()
  }
}

function onShowNotFoundAddressInput(e: any) {
  e.preventDefault()
  inputs.fullAddress.resetNext()
  showNotFoundAddressInput.value = true
  successMessage.value = withoutCoverageMessage
}

async function setValuesFromRecoveryData() {
  if (!recoveryData) return

  if (recoveryData.streetNumber) {
    recoveringStreetNumber.value = true
    streetNumber.value = recoveryData.streetNumber
    snChecked.value = recoveryData.streetNumber === 's/n'
    inputStreetNumber.value = {
      inputValue: recoveryData.streetNumber === 's/n' ? '' : recoveryData.streetNumber
    }
    showNotFoundAddress.value = true
    showNotFoundAddressInput.value = true
  }

  if (recoveryData.town) {
    recoveringTown.value = true
    await setTowns(recoveryData.town)
    town.value = towns.value.find((d) => d.ineCode === recoveryData.ineCode)
    await setGescal7(recoveryData.town || '', recoveryData.ineCode || '')
  }

  if (recoveryData.streetType) {
    recoveringStreetType.value = true
    streetType.value = streetTypes.find((d) => d.key === recoveryData.streetType)
  }

  if (recoveryData.street) {
    recoveringStreet.value = true

    await setInputStreetValue(recoveryData.street)
    gescal12.value = recoveryData.gescal12 || ''
    await setBuildings()
    street.value = streets.value.find((d) => d.gescal12 === recoveryData.gescal12)
  }

  if (recoveryData.streetNumber) {
    await setUnits(recoveryData.gescal17 || '')
    streetNumber.value = recoveryData.streetNumber
  }
  if (recoveryData.zip) {
    inputPostalCode.value.inputValue = recoveryData.zip
    zip.value = recoveryData.zip
  }

  if (bises.value.length > 0) {
    if (recoveryData.bis) {
      recoveringBis.value = true
      bis.value = { bis: recoveryData.bis }
    } else {
      recoveringBis.value = true
      bis.value = ''
      inputBis.value.inputValue = '-'
    }
    currentBis.value = bises.value
  }

  if (blocks.value.length > 0) {
    if (recoveryData.block) {
      recoveringBlock.value = true
      block.value = { block: recoveryData.block }
    } else {
      recoveringBlock.value = true
      block.value = ''
      inputBlock.value.inputValue = '-'
    }
    currentBlocks.value = blocks.value
  }

  if (gates.value.length > 0) {
    if (recoveryData.gate) {
      recoveringGate.value = true
      gate.value = { gate: recoveryData.gate }
    } else {
      recoveringGate.value = true
      gate.value = ''
      inputGate.value.inputValue = '-'
    }
    currentGates.value = gates.value
  }

  if (letters.value.length > 0) {
    if (recoveryData.letter) {
      recoveringLetter.value = true
      letter.value = { letter: recoveryData.letter }
    } else {
      recoveringLetter.value = true
      letter.value = ''
      inputLetter.value.inputValue = '-'
    }
    currentLetters.value = letters.value
  }

  if (stairs.value.length > 0) {
    if (recoveryData.stair) {
      recoveringStair.value = true
      stair.value = { stair: recoveryData.stair }
    } else {
      recoveringStair.value = true
      stair.value = ''
      inputStair.value.inputValue = '-'
    }
    currentStairs.value = stairs.value
  }

  if (floors.value.length > 0) {
    if (recoveryData.floor) {
      recoveringFloor.value = true
      floor.value = { floor: recoveryData.floor }
      inputFloor.value = { inputValue: floor.value.floor }
    } else {
      recoveringFloor.value = true
      floor.value = ''
      inputFloor.value = { inputValue: '-' }
    }
    currentFloors.value = floors.value
  }

  if (firstHands.value.length > 0) {
    if (recoveryData.firstHand) {
      recoveringFirstHand.value = true
      firstHand.value = {
        firstHand: firstHands.value.find((hand) => hand === `${recoveryData.firstHand}`)
      }
      inputFirstHand.value.inputValue = firstHand.value.firstHand
    } else {
      recoveringFirstHand.value = true
      firstHand.value = ''
      inputFirstHand.value.inputValue = '-'
    }
    currentFirstHands.value = firstHands.value
  }

  if (secondHands.value.length) {
    if (recoveryData.secondHand) {
      recoveringSecondHand.value = true
      secondHand.value = {
        secondHand: secondHands.value.find((hand) => hand === `${recoveryData.secondHand}`)
      }
      inputSecondHand.value.inputValue = secondHands.value.find(
        (hand) => hand === `${recoveryData.secondHand}`
      )
    } else {
      recoveringSecondHand.value = true
      secondHand.value = {
        secondHand: ''
      }
      inputSecondHand.value.inputValue = '-'
    }
    currentSecondHands.value = secondHands.value
  }

  if (recoveryData.fullAddress) {
    fullAddress.value = recoveryData.fullAddress
    showNotFoundAddress.value = true
    showNotFoundAddressInput.value = true
    inputFullAddress.value = { inputValue: recoveryData.fullAddress }
  }
  if (recoveryData.gescal37) {
    gescal37.value = recoveryData.gescal37
  }
  if (recoveryData.humanSpecification) {
    humanSpecification.value = recoveryData.humanSpecification
  }
}
setValuesFromRecoveryData()
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
