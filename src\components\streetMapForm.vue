<template>
  <StreetMapForm
    :config="config"
    :data="data"
    :reset-address="resetAddress"
    :is-saving="isSaving"
    :coverage-validation="coverageValidation"
    :webcomponent="webcomponent"
    :show-address="showAddress"
    @validation="$emit('validation', $event)"
  />
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'streetMapForm'
})
</script>

<script setup lang="ts">
import { StreetMapForm } from './streetMapForm'

// Props
const props = defineProps({
  config: String,
  data: String,
  resetAddress: Number,
  isSaving: Number,
  coverageValidation: Number,
  webcomponent: String,
  showAddress: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['validation'])
</script>
