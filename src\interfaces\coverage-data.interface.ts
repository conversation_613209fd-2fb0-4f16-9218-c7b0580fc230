export interface ICoverage {
  town?: string | null | undefined
  ineCode?: string | null | undefined
  streetType?: string | null | undefined
  street?: string | null | undefined
  location?: string | null | undefined
  streetNumber?: string | null | undefined
  bis?: string | null | undefined
  block?: string | null | undefined
  gate?: string | null | undefined
  stair?: string | null | undefined
  letter?: string | null | undefined
  floor?: string | null | undefined
  firstHand?: string | null | undefined
  secondHand?: string | null | undefined
  fullAddress?: string | null | undefined
  gescal7?: string | null | undefined
  gescal12?: string | null | undefined
  gescal17?: string | null | undefined
  gescal37?: string | null | undefined
  humanSpecification?: string | null | undefined
  zip?: string | null | undefined
  from?: string
  gescal37Possibilities?: string[]
}
