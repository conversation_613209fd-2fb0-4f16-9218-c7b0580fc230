import { padStart } from 'parlem-webcomponents-common'
import type { AddressFormData, Town, Street } from '../types/address.types'

/**
 * Genera el código gescal17 a partir del gescal12 y número de calle
 */
export const generateGescal17 = (gescal12: string, streetNumber: string): string => {
  const validStreetNumber = streetNumber !== '' ? streetNumber : '99999'
  return gescal12 + padStart(validStreetNumber, 5, '0')
}

/**
 * Limpia un valor, convirtiendo '-' en cadena vacía
 */
export const cleanValue = (value: any): string => {
  return value === '-' ? '' : (value || '')
}

/**
 * Genera la dirección completa formateada
 */
export const generateFullAddress = (formData: AddressFormData): string => {
  if (formData.snChecked && !formData.fullAddress) {
    return ''
  }

  if (formData.fullAddress && formData.fullAddress.length) {
    return formData.fullAddress
  }

  const townValue = formData.town
  const streetValue = formData.street
  const streetNumberValue = formData.streetNumber
  const zipValue = formData.zip
  const bisValue = formData.bis
  const blockValue = formData.block
  const gateValue = formData.gate
  const letterValue = formData.letter
  const stairValue = formData.stair
  const floorValue = formData.floor
  const firstHandValue = formData.firstHand
  const secondHandValue = formData.secondHand

  const fullStreetName = [
    streetValue?.streetType, 
    streetValue?.streetName, 
    streetNumberValue
  ].join(' ')

  const addressParts = [
    fullStreetName,
    bisValue,
    blockValue,
    gateValue,
    letterValue,
    stairValue,
    floorValue,
    firstHandValue,
    secondHandValue,
    townValue?.name
  ].filter((item) => item && item.length > 0)

  const resultAddress = addressParts.join(', ')
  
  return zipValue ? `${resultAddress} (${zipValue})` : resultAddress
}

/**
 * Tipos de calle con traducciones
 */
export const getStreetTypes = (t: (key: string) => string) => [
  { key: 'all', value: t('streetType.all') },
  { key: 'street', value: t('streetType.street') },
  { key: 'road', value: t('streetType.road') },
  { key: 'highway', value: t('streetType.highway') },
  { key: 'roundabout', value: t('streetType.roundabout') },
  { key: 'passage', value: t('streetType.passage') },
  { key: 'square', value: t('streetType.square') },
  { key: 'promenade', value: t('streetType.promenade') },
  { key: 'bulevar', value: t('streetType.bulevar') },
  { key: 'patrol', value: t('streetType.patrol') },
  { key: 'sector', value: t('streetType.sector') },
  { key: 'urbanization', value: t('streetType.urbanization') },
  { key: 'avenue', value: t('streetType.avenue') },
  { key: 'neighborhood', value: t('streetType.neighborhood') },
  { key: 'gardens', value: t('streetType.gardens') },
  { key: 'park', value: t('streetType.park') },
  { key: 'placette', value: t('streetType.placette') },
  { key: 'settlement', value: t('streetType.settlement') },
  { key: 'track', value: t('streetType.track') },
  { key: 'crossing', value: t('streetType.crossing') },
  { key: 'polygon', value: t('streetType.polygon') },
  { key: 'suburb', value: t('streetType.suburb') }
]

/**
 * Mapea los datos de pueblo para incluir el nombre completo
 */
export const mapTownData = (townData: any): Town => ({
  ...townData,
  fullTownName: `${townData.name} (${townData.province})`
})

/**
 * Mapea los datos de calle para incluir el nombre completo
 */
export const mapStreetData = (streetData: any): Street => ({
  ...streetData,
  fullStreetName: `${streetData.streetType} ${streetData.streetName}`
})

/**
 * Valida si un código postal es válido
 */
export const isValidZip = (zip: string): boolean => {
  const numberRegex = /^[0-9]*$/
  return zip !== 'null' && 
         zip !== 'VARIO' && 
         numberRegex.test(zip) && 
         zip.length >= 5
}

/**
 * Jerarquía de campos para reseteo en cascada
 */
export const FIELD_HIERARCHY = [
  'town', 'streetType', 'street', 'streetNumber', 'zip',
  'bis', 'block', 'gate', 'letter', 'stair', 'floor', 'firstHand', 'secondHand'
] as const

/**
 * Obtiene los campos dependientes de un campo dado
 */
export const getDependentFields = (field: string): string[] => {
  const index = FIELD_HIERARCHY.indexOf(field as any)
  if (index === -1) return []
  
  return FIELD_HIERARCHY.slice(index + 1) as string[]
}

/**
 * Constantes de validación
 */
export const VALIDATION_CONSTANTS = {
  MIN_TOWN_SEARCH_LENGTH: 3,
  MIN_ZIP_LENGTH: 5,
  NUMBER_REGEX: /^[0-9]*$/
} as const
