<template>
  <div class="w-full md:w-1/2 md:pr-3 mt-2">
    <PwSelectAutocomplete
      ref="inputStreet"
      :lang="lang"
      :label="t('coverageForm.street.label')"
      :placeholder="t('coverageForm.street.placeholder')"
      name="street"
      :items="streets"
      itemTitle="fullStreetName"
      itemValue="gescal12"
      v-model="modelValue"
      :value="modelValue?.fullStreetName"
      :required="true"
      :disabled="disabled"
      :inputError="error"
      :classSelectorHeight="'max-h-36'"
      :customLabelClass="labelClasses"
      :customInputClass="inputClasses"
      @input="handleInput"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PwSelectAutocomplete, isEmptyValue } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { Street, Town } from '../types/address.types'

interface Props {
  modelValue: Street | null
  streets: Street[]
  error: string
  lang: string
  theme: string
  town: Town | null
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: Street | null): void
  (e: 'input', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const { t } = useI18n()

// Computed para determinar si está deshabilitado
const disabled = computed(() => isEmptyValue(props.town))

// Clases dinámicas
const inputClasses = computed(() => {
  if (props.theme === 'danger') return '!border-danger'
  if (props.error) return '!border-danger'
  if (disabled.value) return '!placeholder-gray'
  return 'border-primary'
})

const labelClasses = computed(() => {
  if (props.theme === 'danger') return '!text-danger'
  if (props.error) return '!text-danger'
  if (disabled.value) return '!text-gray'
  return ''
})

const handleInput = (event: Event) => {
  emit('input', event)
}
</script>
