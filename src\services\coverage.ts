import axios from 'axios'
import type { IApiRes, IHeaders } from '@/services/api/interfaces'

import {
  X_PARLEM_API_KEY,
  CONTENT_TYPE,
  APPLICATION_JSON_PATCH_JSON
} from '@/services/constants/services.constants'

const coverageHeaders: IHeaders = {
  headers: {
    [X_PARLEM_API_KEY]: import.meta.env.VITE_API_KEY_COVERAGE,
    [CONTENT_TYPE]: APPLICATION_JSON_PATCH_JSON
  }
}

const checkCoverage = {
  async checkCoverage(gescal: string): Promise<boolean> {
    try {
      let url: string = ''
      if (gescal.length === 37) {
        url = `${import.meta.env.VITE_BASE_URL}/coverage/api/coverages/units/${gescal}/coverage`
      }
      if (gescal.length === 17) {
        url = `${import.meta.env.VITE_BASE_URL}/coverage/api/coverages/buildings/${gescal}/coverage`
      }
      const response: IApiRes<boolean> = await axios.get(url, coverageHeaders as any)
      return response.data
    } catch (error: any) {
      return error.response.data
    }
  },
  async checkCoverageOld(gescal: string): Promise<boolean> {
    try {
      let url: string = ''
      if (gescal.length === 37) {
        url = `${import.meta.env.VITE_BASE_URL}/coverage/api/coverages/units/${gescal}`
      }
      if (gescal.length === 17) {
        url = `${import.meta.env.VITE_BASE_URL}/coverage/api/coverages/buildings/${gescal}`
      }
      const response: IApiRes<boolean> = await axios.get(encodeURI(url), coverageHeaders as any)
      return response.data
    } catch (error: any) {
      return error.response.data
    }
  }
}

export default checkCoverage
