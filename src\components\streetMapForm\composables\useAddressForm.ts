import { ref, watch, computed } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { padStart, isEmptyValue } from 'parlem-webcomponents-common'
import { SET_COVERAGE } from '@/store/constants/store.constants'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import type { IConfigData } from '@/interfaces/config-data.interface'

import { useFormState } from './useFormState'
import { useValidation } from './useValidation'
import { useAddressServices } from './useAddressServices'
import { useThemeClasses } from './useThemeClasses'

export function useAddressForm(props: any) {
  const store = useStore()
  const { t } = useI18n()
  
  // Configuración
  const configData: IConfigData = JSON.parse(props.config || '{}')
  const recoveryData: ICoverage = JSON.parse(props.data || '{}')
  const lang = configData.lang as string
  const theme = (configData.theme as string) || 'primary'
  const from = (configData.from as string) || ''

  // Estados y composables
  const formState = useFormState()
  const services = useAddressServices()
  const validation = useValidation(formState.formData, formState.errors, formState.options)
  const themeClasses = useThemeClasses(theme)

  // Variables adicionales
  const gescal7s = ref<string[]>([])
  const gescal7 = ref<string>('')
  const gescal12 = ref<string>('')
  const gescal17 = ref<string>('')
  const gescal37 = ref<string>('')
  const gescal37Possibilities = ref<string[]>([])
  const humanSpecification = ref<string>('')
  const location = ref<string>('')
  const units = ref<any[]>([])
  const building = ref<any>({})

  // Tipos de calle
  const streetTypes = [
    { key: 'all', value: t('streetType.all') },
    { key: 'street', value: t('streetType.street') },
    { key: 'road', value: t('streetType.road') },
    { key: 'highway', value: t('streetType.highway') },
    { key: 'roundabout', value: t('streetType.roundabout') },
    { key: 'passage', value: t('streetType.passage') },
    { key: 'square', value: t('streetType.square') },
    { key: 'promenade', value: t('streetType.promenade') },
    { key: 'bulevar', value: t('streetType.bulevar') },
    { key: 'patrol', value: t('streetType.patrol') },
    { key: 'sector', value: t('streetType.sector') },
    { key: 'urbanization', value: t('streetType.urbanization') },
    { key: 'avenue', value: t('streetType.avenue') },
    { key: 'neighborhood', value: t('streetType.neighborhood') },
    { key: 'gardens', value: t('streetType.gardens') },
    { key: 'park', value: t('streetType.park') },
    { key: 'placette', value: t('streetType.placette') },
    { key: 'settlement', value: t('streetType.settlement') },
    { key: 'track', value: t('streetType.track') },
    { key: 'crossing', value: t('streetType.crossing') },
    { key: 'polygon', value: t('streetType.polygon') },
    { key: 'suburb', value: t('streetType.suburb') }
  ]

  // Watchers para cambios en campos
  const watchTownValue = async (event: any) => {
    if (formState.formData.town) {
      formState.formData.town = null
    }
    
    const inputValue = event.target.value
    if (inputValue.length >= 3) {
      const result = await services.fetchTowns(inputValue)
      formState.options.towns = result.data
      if (result.error) {
        formState.updateError('inputTownError', result.error)
      } else {
        formState.clearError('inputTownError')
      }
    }
  }

  const watchStreetTypeValue = async (event: any) => {
    formState.resetDependentFields('streetType')
    
    if (formState.formData.town && formState.formData.streetType) {
      const address = `${formState.formData.streetType.value} ${event.target.value}`
      const gescal7sResult = await services.fetchGescal7s(address, formState.formData.town.ineCode)
      
      if (gescal7sResult.data.length > 0) {
        gescal7s.value = gescal7sResult.data
        const streetsResult = await services.fetchStreets(address, gescal7s.value)
        formState.options.streets = streetsResult.data
        
        if (streetsResult.error) {
          formState.updateError('inputStreetError', streetsResult.error)
        }
      }
    }
  }

  const watchStreetValue = (event: any) => {
    formState.resetDependentFields('street')
    
    if (formState.formData.street) {
      gescal12.value = formState.formData.street.gescal12
      location.value = formState.formData.street.location
    }
  }

  const watchStreetNumberValue = async (event: any) => {
    formState.resetDependentFields('streetNumber')
    
    if (gescal12.value && formState.formData.streetNumber) {
      gescal17.value = getGescal17(gescal12.value, formState.formData.streetNumber)
      
      const unitsResult = await services.fetchUnits(gescal17.value)
      if (unitsResult.data) {
        units.value = unitsResult.data.units
        building.value = unitsResult.data.building
        formState.formData.zip = unitsResult.data.building.zip
        
        // Actualizar opciones
        formState.options.currentBis = unitsResult.data.bis
        formState.options.currentBlocks = unitsResult.data.blocks
        formState.options.currentGates = unitsResult.data.gates
        formState.options.currentLetters = unitsResult.data.letters
        formState.options.currentStairs = unitsResult.data.stairs
        formState.options.currentFloors = unitsResult.data.floors
        formState.options.currentFirstHands = unitsResult.data.firstHands
        formState.options.currentSecondHands = unitsResult.data.secondHands
        
        formState.zipIsDisabled = 
          formState.formData.zip !== 'null' &&
          formState.formData.zip !== '' &&
          formState.formData.zip !== 'VARIO' &&
          services.numberRegex.test(formState.formData.zip)
      }
    }
  }

  // Función para generar gescal17
  const getGescal17 = (gescal12: string, streetNumber: string): string => {
    const validStreetNumber = streetNumber !== '' ? streetNumber : '99999'
    return gescal12 + padStart(validStreetNumber, 5, '0')
  }

  // Función para obtener datos de cobertura
  const getCoverageData = (): ICoverage => {
    const coverageData: ICoverage = {
      town: formState.formData.town?.name || '',
      ineCode: formState.formData.town?.ineCode || '',
      zip: formState.formData.zip,
      streetType: formState.formData.street?.streetType || formState.formData.streetType?.key || '',
      street: formState.formData.street?.streetName || '',
      location: formState.formData.street?.location || '',
      streetNumber: formState.formData.snChecked ? 's/n' : formState.formData.streetNumber,
      bis: checkValue(formState.formData.bis),
      block: checkValue(formState.formData.block),
      gate: checkValue(formState.formData.gate),
      stair: checkValue(formState.formData.stair),
      letter: checkValue(formState.formData.letter),
      floor: checkValue(formState.formData.floor),
      firstHand: checkValue(formState.formData.firstHand),
      secondHand: checkValue(formState.formData.secondHand),
      fullAddress: formState.formData.fullAddress,
      gescal7: gescal7.value,
      gescal12: gescal12.value,
      gescal17: gescal17.value,
      gescal37: gescal37.value,
      humanSpecification: humanSpecification.value,
      from: from
    }

    if (gescal37Possibilities.value.length) {
      coverageData.gescal37Possibilities = gescal37Possibilities.value
    }

    store.dispatch(SET_COVERAGE, coverageData)
    return coverageData
  }

  const checkValue = (value: any): string => {
    return value === '-' ? '' : (value || '')
  }

  // Función para generar dirección completa
  const getFullAddress = (): string => {
    if (formState.formData.snChecked && !formState.formData.fullAddress) {
      return ''
    }

    if (formState.formData.fullAddress && formState.formData.fullAddress.length) {
      return formState.formData.fullAddress
    }

    const townValue = formState.formData.town
    const streetValue = formState.formData.street
    const streetNumberValue = formState.formData.streetNumber
    const zipValue = formState.formData.zip
    const bisValue = formState.formData.bis
    const blockValue = formState.formData.block
    const gateValue = formState.formData.gate
    const letterValue = formState.formData.letter
    const stairValue = formState.formData.stair
    const floorValue = formState.formData.floor
    const firstHandValue = formState.formData.firstHand
    const secondHandValue = formState.formData.secondHand

    const fullStreetName = [streetValue?.streetType, streetValue?.streetName, streetNumberValue].join(' ')
    const resultAddress = `${[
      fullStreetName,
      bisValue,
      blockValue,
      gateValue,
      letterValue,
      stairValue,
      floorValue,
      firstHandValue,
      secondHandValue,
      townValue?.name
    ]
      .filter((item) => item && item.length > 0)
      .join(', ')}${zipValue ? ' (' + zipValue + ')' : ''}`

    return resultAddress
  }

  // Manejar cambio de checkbox S/N
  const onSNCheckedChange = () => {
    if (formState.formData.snChecked) {
      formState.formData.streetNumber = ''
    }
    formState.showAdditionalInputs = !formState.formData.snChecked
  }

  // Mostrar input de dirección manual
  const onShowNotFoundAddressInput = () => {
    formState.showNotFoundAddressInput = true
  }

  // Watcher para validación
  watch(validation.isValidForm, (isValid) => {
    getFullAddress()
    getCoverageData()
    // Emitir evento de validación al componente padre
  })

  // Función de reset
  const reset = () => {
    formState.resetAll()
    gescal7s.value = []
    gescal7.value = ''
    gescal12.value = ''
    gescal17.value = ''
    gescal37.value = ''
    gescal37Possibilities.value = []
    humanSpecification.value = ''
    location.value = ''
    units.value = []
    building.value = {}
  }

  return {
    // Estado del formulario
    formData: formState.formData,
    errors: formState.errors,
    loading: formState.loading,
    options: formState.options,
    showAdditionalInputs: formState.showAdditionalInputs,
    showNotFoundAddress: formState.showNotFoundAddress,
    showNotFoundAddressInput: formState.showNotFoundAddressInput,
    zipIsDisabled: formState.zipIsDisabled,

    // Configuración
    lang,
    theme,
    streetTypes,

    // Validación
    isValidForm: validation.isValidForm,

    // Clases de tema
    themeClasses,

    // Funciones de eventos
    watchTownValue,
    watchStreetTypeValue,
    watchStreetValue,
    watchStreetNumberValue,
    onSNCheckedChange,
    onShowNotFoundAddressInput,

    // Utilidades
    getCoverageData,
    getFullAddress,
    reset,

    // Servicios
    services
  }
}
