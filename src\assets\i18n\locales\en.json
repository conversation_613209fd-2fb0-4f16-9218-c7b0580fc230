{"coverage_form": {"check_coverage": "Check coverage"}, "coverage": {"description": "By providing your address we will be able to know if you have coverage"}, "streetType": {"all": "All", "avenue": "Avenue", "bulevar": "Bulevar", "crossing": "Crossing", "gardens": "Gardens", "highway": "Highway", "neighborhood": "Neighborhood", "passage": "Passage", "park": "Park", "patrol": "Patrol", "placette": "Placette", "polygon": "Polygon", "promenade": "Promenade", "road": "Road", "roundabout": "Roundabout", "sector": "Sector", "settlement": "Settlement", "square": "Square", "street": "Street", "suburb": "Suburb", "track": "Track", "urbanization": "Urbanization"}, "coverageForm": {"town": {"label": "Municipality", "placeholder": "Search municipality..."}, "streetType": {"label": "Street type", "placeholder": "Select street type..."}, "street": {"label": "Street", "placeholder": "Search street..."}, "streetNumber": {"label": "Street number", "placeholder": "Search street number..."}, "postalCode": {"label": "Zip", "placeholder": "Insert zip code..."}, "bis": {"label": "Bis", "placeholder": "Search bis..."}, "gate": {"label": "Gate", "placeholder": "Search gate..."}, "block": {"label": "Block", "placeholder": "Search block..."}, "letter": {"label": "Letter", "placeholder": "Search letter..."}, "stair": {"label": "Stair", "placeholder": "Search stair..."}, "floor": {"label": "Floor", "placeholder": "Search floor..."}, "firstHand": {"label": "Doors", "placeholder": "Search door..."}, "secondHand": {"label": "2", "placeholder": "Search second door..."}, "fullAddress": {"label": "Complete address", "placeholder": "Complete address", "especification": "Especification", "especification-dots": "House, farmhouse, apartment 4A..."}, "validate": "Check coverage", "save": "Save address", "clear": "Clean values", "coverageMessage": "Perfect! you have coverage.", "fullCoverageMessage": "Perfect! you have coverage.", "fullCoverageMessageIRU": "This address is in IRU zone", "withoutCoverageMessage": "We couldn't find coverage, please contact commercial support.", "findAddressManually": "If you can't find your exact address, click", "inputError": "No information was found regarding the entered data.", "inputErrorBuilding": "We did not find your number.", "invalidInputValue": "The current value is not valid.", "inputErrorNumber": "We did not find your number, enter the zip code and specify the address.", "clickHere": "here"}, "coverageWeb": {"save": "Check if fiber is available at your home"}}