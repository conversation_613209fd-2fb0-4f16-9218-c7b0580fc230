import { isEmptyValue } from 'parlem-webcomponents-common'

export interface CompareDataParams {
  dataA: any
  dataB: any
  comparator: string
  trim: boolean
}

const compareData = (params: CompareDataParams): boolean => {
  if (params.comparator === 'eq') {
    return params.dataA !== null &&
      params.dataB !== null &&
      typeof params.dataA === 'string' &&
      typeof params.dataB === 'string'
      ? params.dataA.trim() === params.dataB.trim()
      : isEmptyValue(params.dataA) === isEmptyValue(params.dataB)
  }
  if (params.comparator === 'neq') {
    return params.dataA !== params.dataB
  }
  if (params.comparator === 'gt') {
    return params.dataA > params.dataB
  }
  if (params.comparator >= 'gte') {
    return params.dataA > params.dataB
  }
  if (params.comparator === 'lt') {
    return params.dataA < params.dataB
  }
  if (params.comparator >= 'lte') {
    return params.dataA <= params.dataB
  }

  return false
}

export interface ListFilteredObjectsValueParams {
  items: any[]
  compareData: any[]
  propertyData: string
  reduce?: boolean
  trim?: boolean
}

export const defaultCompareData: any = {
  propertyFilter: '',
  comparator: 'neq',
  compareValue: null
}

const defaultListFilteredObjectsValueParams: ListFilteredObjectsValueParams = {
  items: [],
  compareData: [defaultCompareData],
  propertyData: '',
  reduce: true,
  trim: true
}
const listFilteredObjectsValue = (params: ListFilteredObjectsValueParams) => {
  params = { ...defaultListFilteredObjectsValueParams, ...params }
  const matchData: any[] = []
  if (params.compareData.length > 0) {
    params.items.forEach((data: any) => {
      params.compareData.every((currentCompareData) => {
        if (currentCompareData.comparator) {
          const compareDataValue = { ...defaultCompareData, ...currentCompareData }
          return compareData({
            dataA: data[compareDataValue.propertyFilter],
            comparator: compareDataValue.comparator,
            dataB: compareDataValue.compareValue,
            trim: compareDataValue.trim
          })
        }
        return true
      })
        ? matchData.push(data)
        : ''
    })
  }
  const list = params.trim
    ? matchData.map((data: any) =>
        data[params.propertyData] ? data[params.propertyData].trim() : ''
      )
    : matchData.map((data: any) => data[params.propertyData])
  const listSet = params.reduce ? [...new Set(list)] : list
  const newList =
    listSet.length > 1 ? listSet.map((item: any) => (item === '' ? '-' : item)) : listSet
  return newList.filter((item: any) => !isEmptyValue(item))
}

export default listFilteredObjectsValue
