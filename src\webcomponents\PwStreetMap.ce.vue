<template>
  <div>
    <streetMapForm
      @validation="validation"
      :config="config"
      :coverageValidation="coverageValidation"
      :data="data"
      :showAddress="false"
      :resetAddress="resetAddress"
      :isSaving="isSaving"
      :theme="theme"
      webcomponent="streetMap"
    ></streetMapForm>
    <div class="flex justify-between items-center">
      <PwButton
        v-if="back === 'true'"
        @click="backEvent"
        class="w-[60px] max-w-[60px] h-[40px] mr-2"
        :theme="'light'"
      >
        <font-awesome-icon icon="fa-solid fa-angle-left" class="max-w-[15px] text-gray" />
      </PwButton>
      <PwButton
        :class="back === 'true' ? 'ml-2' : ''"
        class="mr-2"
        @click="reset"
        :theme="'secondary'"
        :text="resetButtonText ? resetButtonText : t('coverageForm.clear')"
        :type="BtnTypeEnum.reset"
      >
      </PwButton>
      <PwButton
        class="ml-2"
        @click="onSubmit"
        :text="submitButtonText ? submitButtonText : t('coverageForm.save')"
        :disabled="!isValidForm"
        :type="BtnTypeEnum.submit"
        :theme="theme ? theme : 'primary-white'"
      >
      </PwButton>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'PwStreetMap'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { Ref } from 'vue'
import { ref, computed } from 'vue'
import streetMapForm from '../components/streetMapForm.vue'
import { BtnTypeEnum, PwButton } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import type { IConfigData } from '@/interfaces/config-data.interface'
import { GET_COVERAGE } from '@/store/constants/store.constants'

const store: Store<any> = useStore()
const props = defineProps({
  config: String,
  data: String
})
const configData: IConfigData = JSON.parse(props.config || '{}')
const lang: string | undefined = configData.lang as string
const theme: string | undefined = configData.theme as string
let coverageValidation: Ref<number> = ref<number>(-1)
const back = configData.back
const resetButtonText = configData.resetButtonText ? configData.resetButtonText : null
const submitButtonText = configData.submitButtonText ? configData.submitButtonText : null
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
let isValidForm: Ref<Boolean> = ref<boolean>(false)
let resetAddress: Ref<number> = ref<number>(1)
let isSaving: Ref<number> = ref<number>(1)
const address = computed(() => store.getters[`${GET_COVERAGE}`])

function reset() {
  ++resetAddress.value
  const resetCoverageEvent = new CustomEvent('reset-coverage-event', {
    detail: { action: 'reset' }
  })
  window.dispatchEvent(resetCoverageEvent)
}

function onSubmit(e: Event) {
  e.preventDefault()
  --coverageValidation.value
  if (!isValidForm.value) {
    return
  }
  const submitCoverageEvent = new CustomEvent('submit-coverage-event', { detail: address.value })
  window.dispatchEvent(submitCoverageEvent)
}

function backEvent(): void {
  const backCoverageEvent = new CustomEvent('back-coverage-event', { detail: { action: 'back' } })
  window.dispatchEvent(backCoverageEvent)
}

function validation(validation: boolean): void {
  isValidForm.value = validation
}
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
