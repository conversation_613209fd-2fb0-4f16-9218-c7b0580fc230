<template>
  <streetMapForm
    @validation="validation"
    :config="config"
    :data="data"
    :coverageValidation="coverageValidation"
    :resetAddress="resetAddress"
    :showAddress="showAddress"
    :isSaving="isSaving"
    webcomponent="coverage"
  ></streetMapForm>
  <div class="grid gap-4 md:grid-cols-2 mt-2">
    <PwButton
      @click="onValidate"
      :text="t('coverageForm.validate')"
      :type="BtnTypeEnum.button"
      :disabled="!isValidForm"
      :theme="'primary-white'"
    >
    </PwButton>

    <div class="flex flex-col justify-center">
      <p
        v-if="showMessage"
        class="block text-sm font-bold text-primary"
        :class="isIRUZone ? 'mb-1' : ''"
      >
        {{ successMessage || withoutCoverageMessage }}
      </p>
      <p v-if="isIRUZone" class="block text-sm font-bold text-secondary">
        {{ fullCoverageMessageIRU }}
      </p>
    </div>
  </div>

  <div class="flex justify-between items-center">
    <PwButton
      v-if="back === 'true'"
      @click="backEvent"
      class="w-[60px] max-w-[60px] h-[40px] mr-2"
      :theme="'light'"
    >
      <font-awesome-icon icon="fa-solid fa-angle-left" class="max-w-[15px] text-gray" />
    </PwButton>
    <PwButton
      :class="back === 'true' ? 'ml-2' : ''"
      class="mr-2"
      @click="reset"
      :theme="'secondary'"
      :text="resetButtonText ? resetButtonText : t('coverageForm.clear')"
      :type="BtnTypeEnum.reset"
    >
    </PwButton>
    <PwButton
      class="ml-2"
      @click="onSubmit"
      :text="submitButtonText ? submitButtonText : t('coverageForm.save')"
      :disabled="!canContinue"
      :type="BtnTypeEnum.submit"
      :theme="theme ? theme : 'primary-white'"
    >
    </PwButton>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'PwCoverage'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { Ref } from 'vue'
import { ref, computed, onBeforeMount } from 'vue'
import { BtnTypeEnum, changeColors, PwButton } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import streetMapForm from '../components/streetMapForm.vue'
import {
  CHECK_COVERAGE_OLD,
  GET_COVERAGE,
  GET_COVERAGE_RESPONSE
} from '@/store/constants/store.constants'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import type { IConfigData } from '@/interfaces/config-data.interface'

const store: Store<any> = useStore()
const props = defineProps({
  config: String,
  data: String
})
const configData: IConfigData = JSON.parse(props.config || '{}')
const lang: string | undefined = configData.lang as string
const theme: string = configData.theme as string
const back = configData.back
const resetButtonText = configData.resetButtonText ? configData.resetButtonText : null
const submitButtonText = configData.submitButtonText ? configData.submitButtonText : null
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
const fullCoverageMessage = t('coverageForm.fullCoverageMessage')
const fullCoverageMessageIRU = t('coverageForm.fullCoverageMessageIRU')
const withoutCoverageMessage = t('coverageForm.withoutCoverageMessage')
let showMessage: Ref<Boolean> = ref<boolean>(false)
const showAddress: Ref<boolean | undefined> = ref(false)
let canContinue: Ref<Boolean> = ref<boolean>(false)
let successMessage = ref('')
let isIRUZone = ref(false)
let isValidForm: Ref<Boolean> = ref<boolean>(false)
let resetAddress: Ref<number> = ref<number>(1)
let coverageValidation: Ref<number> = ref<number>(1)
let isSaving: Ref<number> = ref<number>(1)
const address = computed(() => store.getters[`${GET_COVERAGE}`])
const coverageResponse = computed(() => store.getters[`${GET_COVERAGE_RESPONSE}`])
const isLoading: Ref<boolean> = ref(false)

onBeforeMount(() => {
  isLoading.value = true
  if (configData.company) {
    changeColors(configData.company ? configData.company : 'Parlem')
  }
  isLoading.value = false
})

function reset() {
  ++resetAddress.value
  isIRUZone.value = false
  showMessage.value = false
  canContinue.value = false
  showAddress.value = false
  const resetCoverageEvent = new CustomEvent('reset-coverage-event', {
    detail: { action: 'reset' }
  })
  window.dispatchEvent(resetCoverageEvent)
}

function onSubmit(e: Event) {
  e.preventDefault()

  if (!canContinue.value) {
    return
  }

  const submitCoverageEvent = new CustomEvent('submit-coverage-event', {
    detail: { address: address.value, coverageResponse: coverageResponse.value }
  })
  window.dispatchEvent(submitCoverageEvent)
}

async function onValidate(e: Event) {
  e.preventDefault()

  ++coverageValidation.value
  showMessage.value = false
  canContinue.value = false
  successMessage.value = ''
  isIRUZone.value = false
  showAddress.value = true

  if (address.value.gescal17?.endsWith('99999')) {
    successMessage.value = withoutCoverageMessage
    isIRUZone.value = false
    showMessage.value = true
    canContinue.value = false
    return
  }

  if (isValidForm.value) {
    if (address.value.gescal37?.length || address.value.gescal17?.length) {
      if (address.value.gescal37Possibilities) {
        for (const gescal37 of address.value.gescal37Possibilities) {
          const coverageResponse = await store.dispatch(`${CHECK_COVERAGE_OLD}`, gescal37)

          if (coverageResponse.coverages.length) {
            successMessage.value = fullCoverageMessage
            address.value.gescal37 = coverageResponse.gescal37
            showMessage.value = true
            canContinue.value = coverageResponse.coverages.length
            return coverageResponse.gescal37
          } else {
            successMessage.value = withoutCoverageMessage
            showMessage.value = true
            canContinue.value = coverageResponse.coverages.length
          }
        }
      } else {
        const coverageResponse = await store.dispatch(
          `${CHECK_COVERAGE_OLD}`,
          address.value.gescal37 || address.value.gescal17
        )
        if (coverageResponse.coverages.length) {
          successMessage.value = fullCoverageMessage
          address.value.gescal37 = coverageResponse.gescal37
        } else {
          successMessage.value = withoutCoverageMessage
        }
        showMessage.value = true
        canContinue.value = coverageResponse.coverages.length
      }
    }
  }
}

function backEvent(): void {
  const backCoverageEvent = new CustomEvent('back-coverage-event', { detail: { action: 'back' } })
  window.dispatchEvent(backCoverageEvent)
}

function validation(isValid: boolean): void {
  isValidForm.value = isValid
}
</script>

<style>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
