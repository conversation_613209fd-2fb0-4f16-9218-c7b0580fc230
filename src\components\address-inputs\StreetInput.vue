<template>
  <PwSelectAutocomplete
    v-model="streetField.value"
    :id="streetField.name"
    :name="streetField.itemTitle"
    :value="streetField.value?.[streetField.itemTitle]"
    :item-title="streetField.itemTitle"
    :item-value="streetField.itemValue"
    :label="`${streetField.label}${streetField.validations?.includes('required') ? ' *' : ''}`"
    :items="streetField.options"
    :classSelectorHeight="'max-h-36'"
    :customInputClass="`rounded-lg h-[60px] font-figtree ${
      theme === 'danger'
        ? '!border-danger'
        : isEmptyValue(selectedTown)
        ? '!bg-[#FAF6F3]/50 !cursor-default'
        : ''
    }`"
    :custom-label-class="customLabelClass"
    :validations="streetField.validations"
    :inputError="t(`${streetField.error}`)"
    :modelObject="true"
    :clear-data-index="clearDataIndex"
    :disabled="isEmptyValue(selectedTown)"
    @input="watchSelector"
  ></PwSelectAutocomplete>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'StreetInput'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { Ref, ComputedRef, PropType } from 'vue'
import { ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import { PwSelectAutocomplete, isEmptyValue } from 'parlem-webcomponents-common'
import {
  SET_SELECTED_STREET,
  GET_SELECTED_TOWN,
  SET_STREETS,
  SET_LOCATIONS,
  GET_LOCATIONS
} from '@/store/constants/store.constants'
import type { ITown } from '@/interfaces/town.interface'
import type { IConfigData } from '@/interfaces/config-data.interface'

const props = defineProps({
  configData: {
    type: Object as PropType<IConfigData>,
    required: true
  }
})

const store: Store<any> = useStore()
const configData: IConfigData = props.configData
const lang: string | undefined = configData.lang as string
const theme: string | undefined = configData.theme as string
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
const selectedTown: ComputedRef<ITown | null> = computed(
  () => store.getters[`${GET_SELECTED_TOWN}`]
)
const locations: ComputedRef<any[] | null> = computed(() => store.getters[`${GET_LOCATIONS}`])
const customLabelClass = ref(
  ` font-figtree !text-black pb-[5px] mb-[5px] ${
    theme === 'danger' ? '!border-danger' : 'border-primary '
  }`
)
const clearDataIndex: Ref<number> = ref(1)

const streetField = ref({
  name: 'street',
  itemTitle: 'fullStreetName',
  itemValue: 'gescal12',
  type: 'selector',
  value: undefined,
  label: t('coverageForm.street.label'),
  placeholder: t('coverageForm.street.placeholder'),
  width: '1/2',
  options: [],
  validations: ['required'],
  error: ''
})

const watchSelector = async (event: any) => {
  if (streetField.value.value) {
    streetField.value.value = undefined
  }
  const inputValue = event.target.value

  if (inputValue.length >= 2) {
    await store.dispatch(`${SET_STREETS}`, {
      inputValue,
      streetField: streetField.value,
      gescal7s: locations.value
    })
  }
}

watch(selectedTown, async (newSelectedTown) => {
  if (newSelectedTown) {
    await store.dispatch(`${SET_LOCATIONS}`, { ineCode: newSelectedTown.ineCode })
  }
  if (newSelectedTown === null) {
    streetField.value.options = []
    streetField.value.value = undefined
    streetField.value.error = ''
    ++clearDataIndex.value
  }
})

watch(
  () => streetField.value,
  (newStreetField) => {
    if (newStreetField.value) {
      store.dispatch(`${SET_SELECTED_STREET}`, newStreetField.value)
    }
  },
  {
    deep: true
  }
)
</script>
