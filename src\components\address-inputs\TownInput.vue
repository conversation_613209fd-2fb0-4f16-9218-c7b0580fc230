<template>
  <PwSelectAutocomplete
    v-model="townField.value"
    :id="townField.name"
    :name="townField.name"
    :value="townField?.value?.[townField.itemTitle]"
    :item-title="townField.itemTitle"
    :item-value="townField.itemValue"
    :label="`${townField.label}${townField.validations?.includes('required') ? ' *' : ''}`"
    :items="townField.options"
    :classSelectorHeight="'max-h-36'"
    :customInputClass="customInputClass"
    :custom-label-class="customLabelClass"
    :validations="townField.validations"
    :inputError="t(`${townField.error}`)"
    :modelObject="true"
    :disabled="false"
    @input="watchSelector"
  ></PwSelectAutocomplete>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'TownInput'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { ComputedRef, PropType } from 'vue'
import { ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import { PwSelectAutocomplete } from 'parlem-webcomponents-common'
import { SET_TOWNS, SET_SELECTED_TOWN, GET_SELECTED_TOWN } from '@/store/constants/store.constants'
import type { ITown } from '@/interfaces/town.interface'
import type { IConfigData } from '@/interfaces/config-data.interface'

const props = defineProps({
  configData: {
    type: Object as PropType<IConfigData>,
    required: true
  }
})

const store: Store<any> = useStore()
const configData: IConfigData = props.configData
const lang: string | undefined = configData.lang as string
const theme: string | undefined = configData.theme as string
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
const selectedTown: ComputedRef<ITown | null> = computed(
  () => store.getters[`${GET_SELECTED_TOWN}`]
)

const customLabelClass = ref(
  ` font-figtree !text-black pb-[5px] mb-[5px] ${
    theme === 'danger' ? '!border-danger' : 'border-primary '
  }`
)
const customInputClass = ref(
  `rounded-lg h-[60px] font-figtree ${theme === 'danger' ? '!border-danger' : 'border-gray'}`
)
const townField = ref({
  name: 'town',
  itemTitle: 'fullTownName',
  itemValue: 'ineCode',
  value: undefined,
  label: t('coverageForm.town.label'),
  placeholder: t('coverageForm.town.placeholder'),
  width: '1/2',
  options: [],
  validations: ['required'],
  error: ''
})

const watchSelector = async (event: any) => {
  if (townField.value.value) {
    townField.value.value = undefined
  }
  const inputValue = event.target.value

  if ((inputValue.length >= 2 && !selectedTown.value) || selectedTown.value) {
    await store.dispatch(`${SET_TOWNS}`, { inputValue, townField: townField.value })
  }
}

watch(
  () => townField.value,
  (newTownField) => {
    if (newTownField.value) {
      store.dispatch(`${SET_SELECTED_TOWN}`, newTownField.value)
    }
  },
  {
    deep: true
  }
)
</script>

<style></style>
