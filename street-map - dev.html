<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Street Map</title>
    <script src="https://dev-pwc.parlem.com/coverage/parlem-webcomponents-coverage.umd.js"></script>
  </head>
  <body>
    <div id="pw-street-map"></div>
  </body>
  <script>
    // Get params from url Add to url https://example.com/en/sells
    const queryString = window.location.search;
    const urlParams = new URLSearchParams(queryString);
    const config = {
      lang: 'ca',
      back: 'false'
    }
    const data = {
      "town": "Barcelona",
      "ineCode": "",
      "streetType": "",
      "street": "",
      "streetNumber": "",
      "block": "",
      "stair": "",
      "floor": "",
      "firstHand": "",
      "zip":"",
      //"fullAddress": "Avinguda Diagonal, 122, BARCELONA",
      "gescal7": "",
      "gescal12": "",
      "gescal17": "",
      "gescal37": ""
    }
    const configData = urlParams.get('config')||config
    const recoveryData = urlParams.get('data')||data



    // Load webcomponent with dynamic params
    const configStr = JSON.stringify(configData)
    const recoveryDataStr = JSON.stringify(recoveryData)
    
    let streetMap = document.createElement('pw-street-map');
    streetMap.setAttribute("config", configStr);
    streetMap.setAttribute("data", recoveryDataStr);
    const pwStreetMap = document.getElementById('pw-street-map')
    pwStreetMap.appendChild(streetMap);

    // Listener event
    window.addEventListener('submit-coverage-event', (e) => {
      const response = e.detail 
      console.log(response)
    })

    window.addEventListener('back-coverage-event', (e) => {
      const response = e.detail 
      console.log(response)
    })
  </script>
</html>
