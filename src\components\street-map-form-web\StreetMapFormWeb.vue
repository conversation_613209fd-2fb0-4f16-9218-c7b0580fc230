<template>
  <form v-on:submit="onSubmit" id="form-coverage-web">
    <TownInput :config-data="configData" class="mb-[20px]"></TownInput>
    <div class="flex w-full">
      <StreetInput :config-data="configData" class="w-full mr-5"></StreetInput>
      <NumberInput :config-data="configData" class="w-[180px]"></NumberInput>
    </div>
    <div class="flex justify-end">
      <button
        type="submit"
        form="form-coverage-web"
        :disabled="isEmptyValue(gescal17)"
        :class="
          isEmptyValue(gescal17)
            ? 'bg-[#FAF6F3] text-black/50'
            : 'cursor-pointer bg-primary text-secondary hover:bg-primary-light transition ease-in-out duration-1000'
        "
        class="w-fit mt-[50px] font-bold py-[15px] px-[40px] rounded-lg flex justify-center items-center"
      >
        {{ t('coverageWeb.save') }}
      </button>
    </div>
  </form>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'StreetMapFormWeb'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import { computed } from 'vue'
import type { PropType, ComputedRef } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import TownInput from '@/components/address-inputs/TownInput.vue'
import StreetInput from '@/components/address-inputs/StreetInput.vue'
import NumberInput from '@/components/address-inputs/NumberInput.vue'
import {
  GET_GESCAL_17,
  GET_SELECTED_BUILDING,
  CHECK_COVERAGE,
  GET_SELECTED_STREET
} from '@/store/constants/store.constants'
import { isEmptyValue } from 'parlem-webcomponents-common'
import type { IConfigData } from '@/interfaces/config-data.interface'

const props = defineProps({
  configData: {
    type: Object as PropType<IConfigData>,
    required: true
  }
})

const store: Store<any> = useStore()
const lang: string | undefined = props.configData.lang as string
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
const gescal17: ComputedRef<string | null> = computed(() => store.getters[`${GET_GESCAL_17}`])
const selectedBuilding: ComputedRef<any | null> = computed(
  () => store.getters[`${GET_SELECTED_BUILDING}`]
)
const selectedStreet: ComputedRef<any | null> = computed(
  () => store.getters[`${GET_SELECTED_STREET}`]
)

const checkBuildingCoverage = async () => {
  const coverageResponse = await store.dispatch(`${CHECK_COVERAGE}`, gescal17.value)
  return coverageResponse
}
const getBuildingToSend = () => {
  if (selectedBuilding.value) return selectedBuilding.value
  selectedStreet.value.gescal17 = gescal17.value
  selectedStreet.value.number = getNumber(gescal17.value)
  return selectedStreet.value
}

const getNumber = (gescal17: string | null) => {
  if (gescal17) {
    let ultims5 = gescal17.slice(-5)
    if (ultims5 === '99999') {
      return 's/n'
    }
    return ultims5.replace(/0/g, '')
  }
}

async function onSubmit(e: Event) {
  e.preventDefault()
  const coverageResponse = await checkBuildingCoverage()

  const submitCoverageEvent = new CustomEvent('submit-coverage-event', {
    detail: {
      coverageResult: coverageResponse.result,
      building: getBuildingToSend()
    }
  })
  window.dispatchEvent(submitCoverageEvent)
}
</script>

<style scoped>
@import '@/assets/styles/index';
@import '~/parlem-webcomponents-common/dist/parlem-webcomponents-common.css';
</style>
