<template>
  <div v-if="showNotFoundAddress || showAddress" class="grid gap-4 md:grid-cols-2 mb-4 mt-2">
    <p v-if="!showNotFoundAddressInput">
      {{ t('coverageForm.findAddressManually') }}
      <a class="text-primary underline cursor-pointer" @click="handleShowInput">
        {{ t('coverageForm.clickHere') }}
      </a>.
    </p>
    
    <div v-if="showNotFoundAddressInput" class="flex">
      <PwInputText
        ref="inputFullAddress"
        class="w-full"
        :lang="lang"
        name="full_address"
        :label="t('coverageForm.fullAddress.especification')"
        :placeholder="t('coverageForm.fullAddress.especification-dots')"
        :value="getFullAddress()"
        v-model="modelValue"
        :inputError="error"
        @input="handleInput"
        @click="handleInput"
        :customInputClass="inputClasses"
        :custom-label-class="labelClasses"
      />
      <div
        @click="handleClose"
        class="-ml-[20px] cursor-pointer w-5 h-5 rounded-full flex items-center justify-center bg-primary-light text-primary hover:text-white hover:bg-primary"
        style="z-index: 101"
      >
        <font-awesome-icon icon="xmark" class="w-3" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PwInputText } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'

interface Props {
  modelValue: string
  error: string
  showNotFoundAddress: boolean
  showNotFoundAddressInput: boolean
  showAddress: boolean
  lang: string
  theme: string
  getFullAddress: () => string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'update:showNotFoundAddressInput', value: boolean): void
  (e: 'input', event: Event): void
  (e: 'showInput'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()

// Clases dinámicas
const inputClasses = computed(() => {
  if (props.error) return '!border-danger'
  return props.theme === 'danger' ? '!border-danger' : 'border-primary'
})

const labelClasses = computed(() => {
  if (props.error) return '!text-danger'
  return props.theme === 'danger' ? '!text-danger' : 'text-primary'
})

// Handlers
const handleInput = (event: Event) => {
  emit('input', event)
}

const handleShowInput = () => {
  emit('update:showNotFoundAddressInput', true)
  emit('showInput')
}

const handleClose = () => {
  emit('update:showNotFoundAddressInput', false)
  emit('update:modelValue', '')
}
</script>
