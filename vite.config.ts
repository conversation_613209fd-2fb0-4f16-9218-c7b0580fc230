import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve, dirname } from 'node:path'
import VueI18nPlugin from '@intlify/unplugin-vue-i18n/vite'

// https://vitejs.dev/config/
export default ({ mode }: any) => {
  process.env = {
    ...process.env,
    ...loadEnv(mode, process.cwd())
  }

  return defineConfig({
    mode: JSON.stringify(process.env.VITE_NODE_ENV),
    build: {
      lib: {
        // Could also be a dictionary or array of multiple entry points
        entry: resolve(__dirname, 'src/main.ts'),
        name: 'parlem-webcomponents-coverage',
        // the proper extensions will be added
        formats: ["umd"],
        fileName: format => `parlem-webcomponents-coverage.${format}.js`
      }
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(process.env.VITE_NODE_ENV)
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: (tag: any) => {
              return tag.includes('pw-')
            },
          }
        }
      }),
      VueI18nPlugin({
        include: resolve(dirname(fileURLToPath(import.meta.url)), './src/assets/i18n/locales/**'),
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '~': fileURLToPath(new URL('./node_modules', import.meta.url)),
      }
    }
  })
}
