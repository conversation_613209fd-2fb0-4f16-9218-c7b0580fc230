<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Coverage web</title>
    <script src="#{cdn_base_url}#/coverage/parlem-webcomponents-coverage.umd.js"></script>
  </head>
  <body>
    <div id="pw-coverage-web"></div>
  </body>
  <script>
    // Get params from url Add to url https://example.com/en/sells
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const config = {
      lang: urlParams.get('lang') || 'ca',
      company: urlParams.get('company') || 'Parlem',
      submitButtonText: urlParams.get('submit-button-text'),
      theme: urlParams.get('theme')
    }

    // Load webcomponent with dynamic params
    const configStr = JSON.stringify(config)
    const recoveryDataStr = JSON.stringify(data)

    let coverage = document.createElement('pw-coverage-web')
    coverage.setAttribute('config', configStr)
    const pwCoverage = document.getElementById('pw-coverage-web')
    pwCoverage.appendChild(coverage)

    // Listener event
    window.addEventListener('submit-coverage-event', (e) => {
      const response = e.detail
      console.log(response)
    })
  </script>
</html>
