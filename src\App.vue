<template>
  <!-- <p>COVERAGE</p>
  <div class="p-4" ref="pwCoverage"></div> -->
  <!-- <p>COVERAGE OLD</p>
  <div class="p-4" ref="pwCoverageOld"></div> -->
  <p>STREETMAP</p>
  <div class="p-4" ref="pwStreetMap"></div>
  <!-- <p>COVERAGE WEB</p>
  <div class="p-4" ref="pwCoverageWeb"></div> -->
</template>

<script lang="ts">
export default {
  name: 'app'
}
</script>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const pwCoverage: any = ref(null)
const pwCoverageOld: any = ref(null)
const pwStreetMap: any = ref(null)
const pwCoverageWeb: any = ref(null)
const configData = {
  lang: 'ca',
  back: 'false',
  from: 'app',
  // theme: 'danger',
  resetButtonText: 'Cancel·lar',
  //submitButtonText: 'Enviar formulari',
  company: 'Parlem'
}
const recoveryData = {
  /* town: 'MANRESA',
      ineCode: '08113',
      zip: '',
      streetType: 'Barri',
      street: 'Cots',
      location: 'MANRESA',
      streetNumber: 's/n',
      fullAddress: '-',
      gescal7: '0800531',
      gescal12: '080009011306',
      gescal17: '08000901130699999',
      gescal37: '',
      humanSpecification: '',
      from: 'app'* /

      /*      town: 'BAIX PALLARS',
      ineCode: '25039',
      zip: '25591',
      streetType: 'Calle',
      street: 'Major',
      streetNumber: '1',
      bis: '',
      block: '',
      location: 'PERAMEA',
      gate: '',
      stair: '',
      letter: '',
      floor: '',
      firstHand: '',
      secondHand: '',
      gescal7: '2500837',
      gescal12: '250023901182',
      gescal17: '25002390118200001',
      gescal37: '',
      humanSpecification: '',
      from: 'app' */
  /*      town: 'PALAFRUGELL',
      ineCode: '17117',
      zip: '17200',
      streetType: 'Calle',
      street: 'Noguera',
      streetNumber: '6',
      bis: '',
      block: '',
      gate: '',
      stair: '',
      letter: '',
      floor: 'chalet',
      firstHand: '',
      secondHand: '',
      location: 'PALAFRUGELL',
      gescal7: '1700329',
      gescal12: '170008302525',
      gescal17: '17000830252500006',
      gescal37: '17000830252500006         CH         ',
      humanSpecification: 'chalet',
      from: 'app' */
  /*    town: 'BARCELONA',
      location: 'BARCELONA',
      ineCode: '08019',
      zip: '08018',
      streetType: 'Calle',
      street: 'Badajoz',
      streetNumber: '145',
      bis: '',
      block: '',
      gate: '',
      stair: '',
      letter: '',
      floor: '2',
      firstHand: '1',
      secondHand: 'oficina',
      gescal7: '0800018',
      gescal12: '080001800361',
      gescal17: '08000180036100145',
      gescal37: '08000180036100145         0021   OFIC',
      humanSpecification: '2º 1ª oficina',
      from: 'app' */
}

onMounted(() => {
  const configStr = JSON.stringify(configData)
  const recoveryDataStr = JSON.stringify(recoveryData)

  let coverage: HTMLElement = document.createElement('pw-coverage')
  coverage.setAttribute('config', configStr)
  coverage.setAttribute('data', recoveryDataStr)
  if (pwCoverage.value) {
    pwCoverage.value.appendChild(coverage)
  }

  let coverageOld: HTMLElement = document.createElement('pw-coverage-old')
  coverageOld.setAttribute('config', configStr)
  coverageOld.setAttribute('data', recoveryDataStr)
  if (pwCoverageOld.value) {
    pwCoverageOld.value.appendChild(coverageOld)
  }

  let streetMap: HTMLElement = document.createElement('pw-street-map')
  streetMap.setAttribute('config', configStr)
  streetMap.setAttribute('data', recoveryDataStr)
  if (pwStreetMap.value) {
    pwStreetMap.value.appendChild(streetMap)
  }

  let coverageWeb: HTMLElement = document.createElement('pw-coverage-web')
  coverageWeb.setAttribute('config', configStr)
  coverageWeb.setAttribute('data', recoveryDataStr)
  if (pwCoverageWeb.value) {
    pwCoverageWeb.value.appendChild(coverageWeb)
  }
})
window.addEventListener('submit-coverage-event', (e: any) => {
  const response = e.detail
  console.log(response)

  type ICoverageResponse = {
    building: IBuilding
    coverageResult: boolean
  }

  type IBuilding = {
    id: number
    gescal2: string
    gescal7: string
    gescal12: string
    gescal17: string
    streetType: string
    streetName: string
    number: string
    zip: string
    town: string
    province: string
    uis: number
  }

  const coverageResponse: ICoverageResponse = {
    building: {
      id: 729800,
      gescal2: '08',
      gescal7: '0800018',
      gescal12: '080001801749',
      gescal17: '08000180174900452',
      streetType: 'Avinguda',
      streetName: 'Diagonal',
      number: '452',
      zip: 'null',
      town: 'BARCELONA',
      province: 'BARCELONA',
      uis: 0
    },
    coverageResult: true
  }
})

window.addEventListener('back-coverage-event', (e: any) => {
  const response = e.detail
  console.log(response)
})
</script>

<style scoped></style>
