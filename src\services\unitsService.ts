import axios from 'axios'
import type { CancelTokenSource } from 'axios'

let source: CancelTokenSource = axios.CancelToken.source()
let pendingCall: boolean = false

const unitsService = {
  get(gescal17: string) {
    if (pendingCall) {
      this.cancel('<PERSON>ici<PERSON> cancel·lada per nova execució')
    }
    this.refreshToken()
    pendingCall = true

    return axios
      .post(
        `${import.meta.env.VITE_BASE_URL}/coverage/api/addresses/units`,
        {
          gescal17
        },
        {
          cancelToken: source.token,
          headers: {
            'x-parlemapikey': import.meta.env.VITE_API_KEY_COVERAGE,
            'content-type': 'application/json-patch+json'
          }
        }
      )
      .then((response: any) => {
        pendingCall = false
        return response && response.data
      })
  },
  cancel(cancelMessage: string) {
    source.cancel(cancelMessage)
    pendingCall = false
    this.refreshToken()
    return this
  },
  refreshToken(): void {
    source = axios.CancelToken.source()
  },
  isPendingCall(): boolean {
    return pendingCall
  }
}

export default unitsService
