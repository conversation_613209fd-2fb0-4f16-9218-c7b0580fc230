import { createStore, type ActionContext } from 'vuex'
import type { IState } from './interfaces/state.interface'
import {
  SET_COVERAGE,
  CHECK_COVERAGE,
  CHECK_COVERAGE_OLD,
  SET_TOWNS,
  SET_SELECTED_TOWN,
  GET_SELECTED_TOWN,
  SET_LOCATIONS,
  GET_LOCATIONS,
  SET_STREETS,
  SET_SELECTED_STREET,
  GET_SELECTED_STREET,
  SET_UNITS,
  SET_SELECTED_BUILDING,
  GET_SELECTED_BUILDING,
  SET_SHOW_ERROR_MESSAGE,
  SET_GESCAL_17
} from './constants/store.constants'
import type { ICoverage } from '@/interfaces/coverage-data.interface'
import apiCoverageService from '@/services/coverage'
import { locationsService, streetsService, townsService, unitsService } from '@/services'
import type { ITown } from '@/interfaces/town.interface'
import type { IStreet } from '@/interfaces/street.interface'
import type { ILocation } from '@/interfaces/location.interface'

export default createStore({
  modules: {
    coverage: {
      state(): IState {
        return {
          coverage: null,
          validCoverage: false,
          coverageResult: null,
          town: null,
          locations: null,
          street: null,
          building: null,
          showErrorMessage: false,
          gescal17: null
        }
      },
      mutations: {
        [SET_COVERAGE](state: IState, coverage: ICoverage): void {
          state.coverage = coverage
        },
        [CHECK_COVERAGE](state: IState, validCoverage: boolean): void {
          state.validCoverage = validCoverage
        },
        [CHECK_COVERAGE_OLD](state: IState, validCoverage: boolean): void {
          state.coverageResult = validCoverage
        },
        [SET_SELECTED_TOWN](state: IState, town: ITown | null): void {
          state.town = town
        },
        [SET_LOCATIONS](state: IState, locations: ILocation[] | null): void {
          state.locations = locations
        },
        [SET_SELECTED_STREET](state: IState, street: IStreet | null): void {
          state.street = street
        },
        [SET_SELECTED_BUILDING](state: IState, building: any): void {
          state.building = building
        },
        [SET_SHOW_ERROR_MESSAGE](state: IState, showErrorMessage: boolean): void {
          state.showErrorMessage = showErrorMessage
        },
        [SET_GESCAL_17](state: IState, gescal17: string | null): void {
          state.gescal17 = gescal17
        }
      },
      actions: {
        [SET_COVERAGE](context: ActionContext<IState, IState>, coverage: ICoverage): void {
          context.commit(SET_COVERAGE, coverage)
        },
        async [CHECK_COVERAGE](
          context: ActionContext<IState, IState>,
          gescal: string
        ): Promise<boolean> {
          const validCoverage: boolean = await apiCoverageService.checkCoverage(gescal)
          context.commit(CHECK_COVERAGE, validCoverage)
          return validCoverage
        },
        async [CHECK_COVERAGE_OLD](
          context: ActionContext<IState, IState>,
          gescal: string
        ): Promise<boolean> {
          const coverageResult: boolean = await apiCoverageService.checkCoverageOld(gescal)
          context.commit(CHECK_COVERAGE_OLD, coverageResult)
          return coverageResult
        },
        //TOWN
        async [SET_TOWNS](
          context: ActionContext<IState, IState>,
          { inputValue, townField }
        ): Promise<string[] | void> {
          await townsService
            .get(inputValue)
            .then((townResult: any) => {
              if (context.state.town) {
                context.commit(SET_SELECTED_TOWN, null)
                context.commit(SET_LOCATIONS, null)
                context.commit(SET_SELECTED_STREET, null)
                context.commit(SET_GESCAL_17, null)
                context.commit(SET_SELECTED_BUILDING, null)
              }
              if (townResult) {
                townResult = townResult.map((townData: any) => {
                  return {
                    ...townData,
                    fullTownName: `${townData.name} (${townData.province})`
                  }
                })
              }
              townField.options = townResult
              townField.error = ''

              return townResult
            })
            .catch((error: any) => {
              if (error.name != 'CanceledError') {
                townField.error = 'coverageForm.inputError'
              }
            })
          return townField
        },
        [SET_SELECTED_TOWN](context: ActionContext<IState, IState>, town: ITown | null): void {
          context.commit(SET_SELECTED_TOWN, town)
        },

        //Locations
        async [SET_LOCATIONS](
          context: ActionContext<IState, IState>,
          { ineCode }
        ): Promise<string[] | void> {
          await locationsService
            .get(ineCode)
            .then((locationResponse: any) => {
              const gescal7s = locationResponse?.map((location: any) => location.gescal7) || []
              if (gescal7s.length === 0) {
                context.commit(SET_SHOW_ERROR_MESSAGE, true)
                return
              }
              context.commit(SET_LOCATIONS, gescal7s)
              context.commit(SET_SHOW_ERROR_MESSAGE, false)
            })
            .catch((error: any) => {
              if (error.name != 'CanceledError') {
                context.commit(SET_SHOW_ERROR_MESSAGE, true)
              }
            })
        },

        //Streets
        async [SET_STREETS](
          context: ActionContext<IState, IState>,
          { inputValue, streetField, gescal7s }
        ): Promise<string[] | void> {
          await streetsService
            .get(inputValue, gescal7s)
            .then((streetResult: any) => {
              if (context.state.street) {
                context.commit(SET_SELECTED_STREET, null)
                context.commit(SET_GESCAL_17, null)
                context.commit(SET_SELECTED_BUILDING, null)
              }
              if (streetResult) {
                streetResult = streetResult.map((streetData: any) => {
                  return {
                    ...streetData,
                    fullStreetName: `${streetData.streetType} ${streetData.streetName} (${streetData.location})`
                  }
                })
                streetField.options = streetResult
                streetField.error = ''
              }
            })
            .catch((error: any) => {
              if (error.name != 'CanceledError') {
                streetField.error = 'coverageForm.inputError'
              }
            })
          return streetField
        },
        [SET_SELECTED_STREET](
          context: ActionContext<IState, IState>,
          street: IStreet | null
        ): void {
          context.commit(SET_SELECTED_STREET, street)
        },

        //buildings
        async [SET_UNITS](
          context: ActionContext<IState, IState>,
          { gescal17, numberField }
        ): Promise<string[] | void> {
          context.commit(SET_GESCAL_17, gescal17)
          gescal17?.length &&
            (await unitsService
              .get(gescal17)
              .then((unitsResponse: any) => {
                context.commit(SET_SELECTED_BUILDING, unitsResponse.building)
                numberField.error = unitsResponse.building ? '' : 'coverageForm.inputErrorBuilding'
              })
              .catch((error: any) => {
                context.commit(SET_SELECTED_BUILDING, null)
                if (error.name != 'CanceledError') {
                  numberField.error = 'coverageForm.inputError'
                }
              }))
          return numberField
        },
        [SET_GESCAL_17](context: ActionContext<IState, IState>, gescal17: string | null): void {
          context.commit(SET_GESCAL_17, gescal17)
        }
      },
      getters: {
        getCoverage: (state: IState): ICoverage | null => {
          return state.coverage
        },
        getCoverageResponse: (state: IState): any => {
          return state.coverageResult
        },
        getSelectedTown: (state: IState): ITown | null => {
          return state.town
        },
        getLocations: (state: IState): ILocation[] | null => {
          return state.locations
        },
        getSelectedStreet: (state: IState): IStreet | null => {
          return state.street
        },
        getSelectedBuilding: (state: IState): any | null => {
          return state.building
        },
        getShowErrorMessage: (state: IState): boolean => {
          return state.showErrorMessage
        },
        getGescal17: (state: IState): string | null => {
          return state.gescal17
        }
      }
    }
  }
})
