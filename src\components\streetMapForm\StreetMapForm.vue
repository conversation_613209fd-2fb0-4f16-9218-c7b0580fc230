<template>
  <div>
    <!-- Primera fila: Ciudad y Tipo de Calle -->
    <div class="flex flex-wrap">
      <TownSelector
        v-model="formData.town"
        :towns="options.towns"
        :error="errors.inputTownError"
        :lang="lang"
        :theme="theme"
        :loading="services.loadingTowns.value"
        @input="watchTownValue"
      />
      
      <StreetTypeSelector
        v-model="formData.streetType"
        :street-types="streetTypes"
        :error="errors.inputStreetTypeError"
        :lang="lang"
        :theme="theme"
        :town="formData.town"
        @input="watchStreetTypeValue"
      />
    </div>

    <!-- Segunda fila: Calle y Número/Código Postal -->
    <div class="flex flex-wrap">
      <StreetSelector
        v-model="formData.street"
        :streets="options.streets"
        :error="errors.inputStreetError"
        :lang="lang"
        :theme="theme"
        :town="formData.town"
        :loading="services.loadingStreets.value"
        @input="watchStreetValue"
      />
      
      <AddressNumberInput
        v-model:street-number="formData.streetNumber"
        v-model:zip="formData.zip"
        v-model:sn-checked="formData.snChecked"
        :street="formData.street"
        :street-number-error="errors.inputStreetNumberError"
        :zip-error="errors.inputPostalCodeError"
        :zip-is-disabled="zipIsDisabled"
        :lang="lang"
        :theme="theme"
        @street-number-input="watchStreetNumberValue"
        @sn-change="onSNCheckedChange"
      />
    </div>

    <!-- Detalles adicionales de la dirección -->
    <AddressDetailsGrid
      v-model:bis="formData.bis"
      v-model:block="formData.block"
      v-model:gate="formData.gate"
      v-model:letter="formData.letter"
      v-model:stair="formData.stair"
      v-model:floor="formData.floor"
      v-model:first-hand="formData.firstHand"
      v-model:second-hand="formData.secondHand"
      :current-bis="options.currentBis"
      :current-blocks="options.currentBlocks"
      :current-gates="options.currentGates"
      :current-letters="options.currentLetters"
      :current-stairs="options.currentStairs"
      :current-floors="options.currentFloors"
      :current-first-hands="options.currentFirstHands"
      :current-second-hands="options.currentSecondHands"
      :bis-error="errors.inputBisError"
      :block-error="errors.inputBlockError"
      :gate-error="errors.inputGateError"
      :letter-error="errors.inputLetterError"
      :stair-error="errors.inputStairError"
      :floor-error="errors.inputFloorError"
      :first-hand-error="errors.inputFirstHandError"
      :second-hand-error="errors.inputSecondHandError"
      :zip="formData.zip"
      :sn-checked="formData.snChecked"
      :show-additional-inputs="showAdditionalInputs"
      :lang="lang"
      :theme="theme"
      @bis-input="watchInputBisValue"
      @block-input="watchInputBlockValue"
      @gate-input="watchInputGateValue"
      @letter-input="watchInputLetterValue"
      @stair-input="watchInputStairValue"
      @floor-input="watchInputFloorValue"
      @first-hand-input="watchInputFirstHandValue"
      @second-hand-input="watchInputSecondHandValue"
    />

    <!-- Dirección manual -->
    <ManualAddressInput
      v-model="formData.fullAddress"
      v-model:show-not-found-address-input="showNotFoundAddressInput"
      :error="errors.inputFullAddressError"
      :show-not-found-address="showNotFoundAddress"
      :show-address="showAddress"
      :lang="lang"
      :theme="theme"
      :get-full-address="getFullAddress"
      @input="watchInputFullAddressValue"
      @show-input="onShowNotFoundAddressInput"
    />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'StreetMapForm'
})
</script>

<script setup lang="ts">
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'

// Componentes
import TownSelector from './components/TownSelector.vue'
import StreetTypeSelector from './components/StreetTypeSelector.vue'
import StreetSelector from './components/StreetSelector.vue'
import AddressNumberInput from './components/AddressNumberInput.vue'
import AddressDetailsGrid from './components/AddressDetailsGrid.vue'
import ManualAddressInput from './components/ManualAddressInput.vue'

// Composables
import { useAddressForm } from './composables/useAddressForm'

// Tipos
import type { IConfigData } from '@/interfaces/config-data.interface'

// Props
const props = defineProps({
  config: String,
  data: String,
  resetAddress: Number,
  isSaving: Number,
  coverageValidation: Number,
  webcomponent: String,
  showAddress: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['validation'])

// Configuración de i18n
const configData: IConfigData = JSON.parse(props.config || '{}')
const lang = configData.lang as string

const { locale } = useI18n()
locale.value = lang || locale.value

// Composable principal
const {
  // Estado del formulario
  formData,
  errors,
  options,
  showAdditionalInputs,
  showNotFoundAddress,
  showNotFoundAddressInput,
  zipIsDisabled,

  // Configuración
  theme,
  streetTypes,

  // Validación
  isValidForm,

  // Funciones de eventos
  watchTownValue,
  watchStreetTypeValue,
  watchStreetValue,
  watchStreetNumberValue,
  onSNCheckedChange,
  onShowNotFoundAddressInput,

  // Utilidades
  getCoverageData,
  getFullAddress,
  reset,

  // Servicios
  services
} = useAddressForm(props)

// Handlers para eventos de campos adicionales
const watchInputBisValue = (event: Event) => {
  // Lógica específica para bis si es necesaria
}

const watchInputBlockValue = (event: Event) => {
  // Lógica específica para block si es necesaria
}

const watchInputGateValue = (event: Event) => {
  // Lógica específica para gate si es necesaria
}

const watchInputLetterValue = (event: Event) => {
  // Lógica específica para letter si es necesaria
}

const watchInputStairValue = (event: Event) => {
  // Lógica específica para stair si es necesaria
}

const watchInputFloorValue = (event: Event) => {
  // Lógica específica para floor si es necesaria
}

const watchInputFirstHandValue = (event: Event) => {
  // Lógica específica para firstHand si es necesaria
}

const watchInputSecondHandValue = (event: Event) => {
  // Lógica específica para secondHand si es necesaria
}

const watchInputFullAddressValue = (event: Event) => {
  // Lógica específica para fullAddress si es necesaria
}

// Watcher para validación
watch(isValidForm, (isValid) => {
  emit('validation', isValid)
})

// Watcher para reset
watch(() => props.resetAddress, () => {
  if (props.resetAddress) {
    reset()
  }
})

// Exponer funciones públicas
defineExpose({
  reset,
  getCoverageData,
  getFullAddress
})
</script>
