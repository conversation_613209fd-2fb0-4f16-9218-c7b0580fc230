<template>
  <div class="relative pr-3">
    <PwInputText
      :id="numberField.name"
      :name="numberField.itemTitle"
      v-model="numberField.value"
      :value="numberField.value?.[numberField.itemTitle]"
      :type="numberField.type"
      :label="numberField.label"
      :clear-data-index="clearDataIndex"
      :disabled="isEmptyValue(selectedStreet)"
      :validations="numberField.validations"
      :customLabelClass="customLabelClass"
      :customInputClass="`rounded-lg h-[60px] font-figtree ${
        theme === 'danger' ? '!border-danger' : 'border-gray'
      } ${isEmptyValue(selectedStreet) ? '!bg-[#FAF6F3]/50 ' : 'bg-white'}`"
      :inputError="t(`${numberField.error}`)"
      @input="watchSelector"
    />
    <div class="absolute inset-y-0 right-0 flex items-start pr-3">
      <label
        for="snCheckbox"
        class="mr-1 mt-1.5 text-sm font-figtree"
        :class="numberField.error ? 'text-error' : 'text-gray'"
        >S/N</label
      >
      <input
        type="checkbox"
        id="snCheckbox"
        v-model="snChecked"
        :clear-data-index="clearDataIndex"
        class="h-4 w-4 mt-2 border-gray"
        @change="onSNCheckedChange"
        :disabled="isEmptyValue(selectedStreet)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'NumberInput'
})
</script>

<script setup lang="ts">
import en from '@/assets/i18n/locales/en.json'
import es from '@/assets/i18n/locales/es.json'
import ca from '@/assets/i18n/locales/ca.json'
import gl from '@/assets/i18n/locales/gl.json'
import va from '@/assets/i18n/locales/va.json'
import type { Ref, ComputedRef, PropType } from 'vue'
import { ref, watch, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import type { Store } from 'vuex'
import { useStore } from 'vuex'
import { PwInputText, isEmptyValue, padStart } from 'parlem-webcomponents-common'
import {
  GET_SELECTED_STREET,
  SET_UNITS,
  GET_GESCAL_17,
  SET_GESCAL_17
} from '@/store/constants/store.constants'
import type { IStreet } from '@/interfaces/street.interface'
import type { IConfigData } from '@/interfaces/config-data.interface'

const props = defineProps({
  configData: {
    type: Object as PropType<IConfigData>,
    required: true
  }
})

const store: Store<any> = useStore()
const configData: IConfigData = props.configData
const lang: string | undefined = configData.lang as string
const theme: string | undefined = configData.theme as string
const { t, locale }: any = useI18n({
  messages: {
    en,
    gl,
    es,
    ca,
    va
  }
})
locale.value = lang || locale.value
const selectedStreet: ComputedRef<IStreet | null> = computed(
  () => store.getters[`${GET_SELECTED_STREET}`]
)
const selectedGescal17: ComputedRef<string | null> = computed(
  () => store.getters[`${GET_GESCAL_17}`]
)
const customLabelClass = ref(
  ` font-figtree !text-black pb-[1px] mb-[5px] max-h-[29px] ${
    theme === 'danger' ? '!border-danger' : 'border-primary '
  }`
)
const clearDataIndex: Ref<number> = ref(1)
const snChecked: Ref<boolean> = ref(false)
const numberField: Ref<any> = ref({
  name: 'streetNumber',
  itemTitle: 'streetNumber',
  itemValue: 'gescal12',
  type: 'number',
  value: undefined,
  label: t('coverageForm.streetNumber.label'),
  placeholder: t('coverageForm.streetNumber.placeholder'),
  width: '1/2',
  validations: ['required'],
  error: ''
})

const setUnits = async (gescal17: string | null) => {
  if (gescal17) {
    await store.dispatch(`${SET_UNITS}`, {
      gescal17,
      numberField: numberField.value
    })
  }
}

const removeGescal17 = () => {
  store.dispatch(`${SET_GESCAL_17}`, null)
}

const watchSelector = async (event: any) => {
  if (selectedGescal17.value) {
    removeGescal17()
  }
  if (numberField.value.value) {
    numberField.value.value = undefined
  }
  if (snChecked.value) {
    snChecked.value = false
  }
  const inputValue = event.target.value
  const gescal17 = getGescal17(selectedStreet.value?.gescal12, inputValue)

  if (inputValue.length >= 1) {
    await setUnits(gescal17)
  }
}

const onSNCheckedChange = async () => {
  if (selectedGescal17.value) {
    removeGescal17()
  }
  if (snChecked.value) {
    numberField.value.value = undefined
    ++clearDataIndex.value

    const gescal17 = getGescal17(selectedStreet.value?.gescal12)
    await setUnits(gescal17)
  }
}

const getGescal17 = (gescal12: string | undefined, streetNumber: string = ''): string | null => {
  if (gescal12) {
    const validStreetNumber = streetNumber != '' ? streetNumber : '99999'
    return gescal12 + padStart(validStreetNumber, 5, '0')
  }
  return null
}

watch(selectedStreet, (newSelectedStreet) => {
  if (newSelectedStreet === null) {
    numberField.value.value = undefined
    numberField.value.error = ''
    snChecked.value = false
    ++clearDataIndex.value
  }
})
</script>
