<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>streetMap</title>
    <script src="https://pwc.parlem.com/coverage/parlem-webcomponents-coverage.umd.js"></script>
  </head>
  <body>
    <div id="pw-street-map"></div>
  </body>
  <script>
    // Get params from url Add to url https://example.com/en/sells
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const config = {
      lang: urlParams.get('lang') || 'ca',
      back: urlParams.get('lang') || 'false',
      gescal37: urlParams.get('gescal37') || ''
    }
    const data = {
      town: 'Barcelona',
      ineCode: '',
      streetType: '',
      street: '',
      streetNumber: '',
      block: '',
      stair: '',
      floor: '',
      firstHand: '',
      zip: '',
      //"fullAddress": "Avinguda Diagonal, 122, BARCELONA",
      gescal7: '',
      gescal12: '',
      gescal17: '',
      gescal37: ''
    }

    // Load webcomponent with dynamic params
    const configStr = JSON.stringify(config)
    const recoveryDataStr = JSON.stringify(data)

    let streetMap = document.createElement('pw-street-map')
    streetMap.setAttribute('config', configStr)
    streetMap.setAttribute('data', recoveryDataStr)
    const pwStreetMap = document.getElementById('pw-street-map')
    pwStreetMap.appendChild(streetMap)

    // Listener event
    window.addEventListener('submit-coverage-event', (e) => {
      const response = e.detail
      console.log(response)
    })

    window.addEventListener('back-coverage-event', (e) => {
      const response = e.detail
      console.log(response)
    })
  </script>
</html>
