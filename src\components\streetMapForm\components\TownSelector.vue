<template>
  <div class="w-full md:w-1/2 md:pr-3 mt-2">
    <PwSelectAutocomplete
      ref="inputTown"
      :lang="lang"
      :label="t('coverageForm.town.label')"
      :placeholder="t('coverageForm.town.placeholder')"
      name="town"
      :items="towns"
      itemTitle="fullTownName"
      itemValue="ineCode"
      v-model="modelValue"
      :value="modelValue?.fullTownName"
      :modelObject="true"
      :required="true"
      :inputError="error"
      :classSelectorHeight="'max-h-36'"
      :customInputClass="inputClasses"
      :custom-label-class="labelClasses"
      @input="handleInput"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PwSelectAutocomplete } from 'parlem-webcomponents-common'
import { useI18n } from 'vue-i18n'
import type { Town } from '../types/address.types'

interface Props {
  modelValue: Town | null
  towns: Town[]
  error: string
  lang: string
  theme: string
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: Town | null): void
  (e: 'input', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

const { t } = useI18n()

// Clases dinámicas basadas en el tema y estado
const inputClasses = computed(() => {
  if (props.error) return '!border-danger'
  return props.theme === 'danger' ? '!border-danger' : 'border-primary'
})

const labelClasses = computed(() => {
  if (props.error) return '!text-danger'
  return props.theme === 'danger' ? '!text-danger' : 'text-primary'
})

const handleInput = (event: Event) => {
  emit('input', event)
}
</script>
