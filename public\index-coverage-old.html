<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Coverage</title>
    <script src="#{cdn_base_url}#/coverage/parlem-webcomponents-coverage.umd.js"></script>
  </head>
  <body>
    <div id="pw-coverage-old"></div>
  </body>
  <script>
    // Get params from url Add to url https://example.com/en/sells
    const queryString = window.location.search
    const urlParams = new URLSearchParams(queryString)
    const config = {
      lang: urlParams.get('lang') || 'ca',
      back: urlParams.get('lang') || 'false',
      gescal37: urlParams.get('gescal37') || ''
    }
    const data = {
      town: 'Barcelona',
      ineCode: '',
      streetType: '',
      street: '',
      streetNumber: '',
      block: '',
      stair: '',
      floor: '',
      firstHand: '',
      zip: '',
      //"fullAddress": "Avinguda Diagonal, 122, BARCELONA",
      gescal7: '',
      gescal12: '',
      gescal17: '',
      gescal37: ''
    }

    // Load webcomponent with dynamic params
    const configStr = JSON.stringify(config)
    const recoveryDataStr = JSON.stringify(data)

    let coverageOld = document.createElement('pw-coverage-old')
    coverageOld.setAttribute('config', configStr)
    coverageOld.setAttribute('data', recoveryDataStr)
    const pwCoverageOld = document.getElementById('pw-coverage-old')
    pwCoverageOld.appendChild(coverageOld)

    // Listener event
    window.addEventListener('submit-coverage-event', (e) => {
      const response = e.detail
      console.log(response)
    })

    window.addEventListener('back-coverage-event', (e) => {
      const response = e.detail
      console.log(response)
    })
  </script>
</html>
