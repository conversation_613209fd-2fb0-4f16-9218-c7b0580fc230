/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./public/**/*.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'media', // or 'media' or 'class'
  theme: {
    extend: {
      fontFamily: {
        figtree: ['Figtree', 'sans-serif'],
        'figtree-italic': ['Figtree-Italic', 'sans-serif']
      }
    },
    colors: {
      primary: 'rgb(var(--color-primary) / <alpha-value>)',
      secondary: 'rgb(var(--color-secondary) / <alpha-value>)',
      'primary-light': 'rgb(var(--color-primary-light) / <alpha-value>)',
      'gray-light': '#E1E1E1',
      white: '#ffffff',
      black: '#000000',
      error: '#9B1C1C',
      danger: '#FB2B2B',
      warning: '#C27803',
      success: '#0E9F6E',
      info: '#6B7280',
      gray: '#c1c1c1'
    }
  },
  variants: {
    extend: {}
  },
  plugins: []
}
